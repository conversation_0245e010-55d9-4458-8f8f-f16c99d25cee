<div x-cloak>
    <!-- Simple Modal Structure -->
    <div
        x-data="{ show: @entangle('showModal').live }"
        x-show="show"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 z-50 overflow-y-auto"
    >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-gray-500 opacity-75 transition-opacity z-40" aria-hidden="true" @click="show = false"></div>

        <!-- Modal Dialog Container -->
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0 z-50 relative">
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <!-- Modal content -->
            <div
                x-show="show"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
                role="dialog" aria-modal="true" aria-labelledby="modal-headline"
                @click.outside="show = false"
            >
                @if ($property)
                    <form wire:submit="sendInquiry" class="p-6">
                        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Contact {{ $property->user->name }}</h2>
                        <p class="text-gray-600 mb-6">Inquiring about: <span class="font-medium">{{ $property->title }}</span></p>

                        @if (session()->has('inquiry_message'))
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                                <span class="block sm:inline">{{ session('inquiry_message') }}</span>
                            </div>
                        @endif

                        <div class="mb-4">
                            <flux:input wire:model.live="name" id="name" type="text" label="{{ __('Your Name') }}" class="mt-1 block w-full" required />
                        </div>

                        <div class="mb-4">
                            <flux:input wire:model.live="email" id="email" type="email" label="{{ __('Your Email') }}" class="mt-1 block w-full" required />
                        </div>

                        <div class="mb-4">
                            <flux:input wire:model.live="phone" id="phone" type="text" label="{{ __('Your Phone (Optional)') }}" class="mt-1 block w-full" />
                        </div>

                        <div class="mb-6">
                            <flux:textarea wire:model.live="message" id="message" label="{{ __('Your Message') }}" rows="5" class="mt-1 block w-full" required></flux:textarea>
                        </div>

                        <div class="flex justify-end gap-4">
                            <flux:button variant="outline" wire:click="$set('showModal', false)" type="button">
                                Cancel
                            </flux:button>
                            <flux:button variant="primary" wire:loading.attr="disabled">
                                Send Inquiry
                            </flux:button>
                        </div>
                    </form>
                @else
                    <div class="p-6 text-center text-gray-600">
                        <p>Loading contact form...</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
