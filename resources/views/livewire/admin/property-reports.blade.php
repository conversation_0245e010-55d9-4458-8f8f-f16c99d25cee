<div>
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Property Reports</h1>
        <p class="mt-2 text-gray-600">Detailed statistics and data about property listings</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Total Properties</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $totalProperties }}</h3>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <flux:icon.home class="h-6 w-6 text-blue-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Published Properties</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $publishedProperties }}</h3>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <flux:icon.check-circle class="h-6 w-6 text-green-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Draft Properties</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $draftProperties }}</h3>
                </div>
                <div class="bg-yellow-100 p-3 rounded-full">
                    <flux:icon.pencil class="h-6 w-6 text-yellow-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Sold Properties</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $soldProperties }}</h3>
                </div>
                <div class="bg-red-100 p-3 rounded-full">
                    <flux:icon.currency-dollar class="h-6 w-6 text-red-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Rented Properties</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $rentedProperties }}</h3>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                    <flux:icon.key class="h-6 w-6 text-purple-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Under Offer</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $underOfferProperties }}</h3>
                </div>
                <div class="bg-gray-100 p-3 rounded-full">
                    <flux:icon.document-text class="h-6 w-6 text-gray-600" />
                </div>
            </div>
        </div>
    </div>

    <!-- Average Price Card -->
    <div class="bg-white p-6 rounded-lg shadow border mb-8">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-500">Average Property Price (Published)</p>
                <h3 class="text-2xl font-bold text-gray-900">USD {{ number_format($averagePrice, 2) }}</h3>
            </div>
            <div class="bg-indigo-100 p-3 rounded-full">
                <flux:icon.chart-bar class="h-6 w-6 text-indigo-600" />
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-6 rounded-lg shadow border mb-6">
        <div class="mb-6">
            <flux:heading size="xl">Search & Filter</flux:heading>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <flux:field>
                    <flux:label>Keywords</flux:label>
                    <flux:input wire:model.live.debounce.500ms="search" placeholder="Search properties..." />
                </flux:field>
            </div>

            <div>
                <flux:field>
                    <flux:label>Status</flux:label>
                    <flux:select wire:model.live="statusFilter" placeholder="All Statuses">
                        <option value="">All Statuses</option>
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                        <option value="sold">Sold</option>
                        <option value="rented">Rented</option>
                        <option value="under_offer">Under Offer</option>
                    </flux:select>
                </flux:field>
            </div>

            <div>
                <flux:field>
                    <flux:label>Property Type</flux:label>
                    <flux:select wire:model.live="typeFilter" placeholder="All Types">
                        <option value="">All Types</option>
                        @foreach ($propertyTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </flux:select>
                </flux:field>
            </div>
        </div>
    </div>

    <!-- Properties Table -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('id')">ID <i class="fas {{ $sortField == 'id' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('title')">Title <i class="fas {{ $sortField == 'title' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('price')">Price <i class="fas {{ $sortField == 'price' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('created_at')">Created <i class="fas {{ $sortField == 'created_at' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach ($properties as $property)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $property->id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $property->title }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $property->propertyType->name ?? 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <flux:badge :variant="$property->status == 'published' ? 'success' : ($property->status == 'draft' ? 'warning' : ($property->status == 'sold' ? 'danger' : ($property->status == 'rented' ? 'info' : 'secondary')))">
                                    {{ ucfirst(str_replace('_', ' ', $property->status)) }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">USD {{ number_format($property->price, 2) }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $property->created_at->format('M d, Y') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="p-6 border-t border-gray-200">
            {{ $properties->links() }}
        </div>
    </div>
</div>
