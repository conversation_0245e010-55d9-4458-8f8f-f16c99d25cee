<div>
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">User Reports</h1>
        <p class="mt-2 text-gray-600">Detailed statistics and data about user accounts</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Total Users</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $totalUsers }}</h3>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <flux:icon.users class="h-6 w-6 text-blue-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Active Users</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $activeUsers }}</h3>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <flux:icon.check-circle class="h-6 w-6 text-green-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Admin Users</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $adminUsers }}</h3>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                    <flux:icon.shield class="h-6 w-6 text-purple-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Agent Users</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $agentUsers }}</h3>
                </div>
                <div class="bg-yellow-100 p-3 rounded-full">
                    <flux:icon.building-office class="h-6 w-6 text-yellow-600" />
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Seeker Users</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $seekerUsers }}</h3>
                </div>
                <div class="bg-gray-100 p-3 rounded-full">
                    <flux:icon.magnifying-glass class="h-6 w-6 text-gray-600" />
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-6 rounded-lg shadow border mb-6">
        <div class="mb-6">
            <flux:heading size="xl">Search & Filter</flux:heading>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <flux:field>
                    <flux:label>Keywords</flux:label>
                    <flux:input wire:model.live.debounce.500ms="search" placeholder="Search users..." />
                </flux:field>
            </div>

            <div>
                <flux:field>
                    <flux:label>Role</flux:label>
                    <flux:select wire:model.live="roleFilter" placeholder="All Roles">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="agent">Agent</option>
                        <option value="seeker">Seeker</option>
                    </flux:select>
                </flux:field>
            </div>

            <div>
                <flux:field>
                    <flux:label>Status</flux:label>
                    <flux:select wire:model.live="statusFilter" placeholder="All Statuses">
                        <option value="">All Statuses</option>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </flux:select>
                </flux:field>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('id')">ID <i class="fas {{ $sortField == 'id' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('name')">Name <i class="fas {{ $sortField == 'name' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('email')">Email <i class="fas {{ $sortField == 'email' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('created_at')">Joined <i class="fas {{ $sortField == 'created_at' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach ($users as $user)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $user->id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $user->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $user->email }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if ($user->hasRole('admin'))
                                    <flux:badge variant="primary">Admin</flux:badge>
                                @elseif ($user->hasRole('agent'))
                                    <flux:badge variant="warning">Agent</flux:badge>
                                @else
                                    <flux:badge variant="secondary">Seeker</flux:badge>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <flux:badge variant="{{ $user->is_active ? 'success' : 'danger' }}">
                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $user->created_at->format('M d, Y') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="p-6 border-t border-gray-200">
            {{ $users->links() }}
        </div>
    </div>
</div>
