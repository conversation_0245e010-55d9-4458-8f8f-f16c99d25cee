<div>
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="mt-2 text-gray-600">Overview of system statistics and recent activity</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Total Users</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $totalUsers }}</h3>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <flux:icon.users class="h-6 w-6 text-blue-600" />
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between text-xs">
                    <span class="text-gray-500">Active</span>
                    <span class="font-medium text-green-600">{{ $activeUsers }}</span>
                </div>
                <div class="flex justify-between text-xs mt-1">
                    <span class="text-gray-500">Inactive</span>
                    <span class="font-medium text-red-600">{{ $inactiveUsers }}</span>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Total Properties</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $totalProperties }}</h3>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <flux:icon.home class="h-6 w-6 text-green-600" />
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between text-xs">
                    <span class="text-gray-500">Published</span>
                    <span class="font-medium text-green-600">{{ $publishedProperties }}</span>
                </div>
                <div class="flex justify-between text-xs mt-1">
                    <span class="text-gray-500">Pending</span>
                    <span class="font-medium text-yellow-600">{{ $pendingProperties }}</span>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">User Roles</p>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $totalUsers }}</h3>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                    <flux:icon.user-group class="h-6 w-6 text-purple-600" />
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between text-xs">
                    <span class="text-gray-500">Admins</span>
                    <span class="font-medium text-blue-600">{{ $adminUsers }}</span>
                </div>
                <div class="flex justify-between text-xs mt-1">
                    <span class="text-gray-500">Agents</span>
                    <span class="font-medium text-green-600">{{ $agentUsers }}</span>
                </div>
                <div class="flex justify-between text-xs mt-1">
                    <span class="text-gray-500">Seekers</span>
                    <span class="font-medium text-yellow-600">{{ $seekerUsers }}</span>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-500">Avg. Property Price</p>
                    <h3 class="text-2xl font-bold text-gray-900">USD {{ number_format($averagePrice, 2) }}</h3>
                </div>
                <div class="bg-yellow-100 p-3 rounded-full">
                    <flux:icon.currency-dollar class="h-6 w-6 text-yellow-600" />
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between text-xs">
                    <span class="text-gray-500">Sold</span>
                    <span class="font-medium text-red-600">{{ $soldProperties }}</span>
                </div>
                <div class="flex justify-between text-xs mt-1">
                    <span class="text-gray-500">Rented</span>
                    <span class="font-medium text-purple-600">{{ $rentedProperties }}</span>
                </div>
                <div class="flex justify-between text-xs mt-1">
                    <span class="text-gray-500">Under Offer</span>
                    <span class="font-medium text-blue-600">{{ $underOfferProperties }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Property Types -->
        <div class="bg-white p-6 rounded-lg shadow border lg:col-span-1">
            <h3 class="text-lg font-semibold mb-4">Property Types</h3>
            <div class="space-y-3 max-h-64 overflow-y-auto">
                @foreach ($propertyTypes as $type => $count)
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">{{ $type }}</span>
                    <span class="font-medium">{{ $count }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: '{{ $propertyTypePercentages[$type] }}%'"></div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Listing Types -->
        <div class="bg-white p-6 rounded-lg shadow border lg:col-span-1">
            <h3 class="text-lg font-semibold mb-4">Listing Types</h3>
            <div class="space-y-3 max-h-64 overflow-y-auto">
                @foreach ($listingTypes as $type => $count)
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">{{ $type }}</span>
                    <span class="font-medium">{{ $count }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-green-600 h-2.5 rounded-full" style="width: '{{ $listingTypePercentages[$type] }}%'"></div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Top Cities -->
        <div class="bg-white p-6 rounded-lg shadow border lg:col-span-1">
            <h3 class="text-lg font-semibold mb-4">Top Cities</h3>
            <div class="space-y-3 max-h-64 overflow-y-auto">
                @foreach ($topCities as $city => $count)
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">{{ $city }}</span>
                    <span class="font-medium">{{ $count }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-purple-600 h-2.5 rounded-full" style="width: '{{ $topCityPercentages[$city] }}%'"></div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Properties -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-4">
                <flux:heading size="xl">Recent Properties</flux:heading>
                <flux:subheading>Latest property listings</flux:subheading>
            </div>
            <div class="space-y-4">
                @forelse($recentProperties as $property)
                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <flux:icon.building-office class="size-6 text-gray-400" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ $property->title }}</p>
                        <p class="text-xs text-gray-500">by {{ $property->user->name }} • {{ $property->created_at->diffForHumans() }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $property->status === 'published' ? 'bg-green-100 text-green-800' :
                                       ($property->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ ucfirst($property->status) }}
                        </span>
                    </div>
                </div>
                @empty
                <p class="text-gray-500 text-sm">No recent properties found.</p>
                @endforelse
            </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-4">
                <flux:heading size="xl">Recent Users</flux:heading>
                <flux:subheading>Latest user registrations</flux:subheading>
            </div>
            <div class="space-y-4">
                @forelse($recentUsers as $user)
                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                            {{ $user->initials() }}
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ $user->name }}</p>
                        <p class="text-xs text-gray-500">{{ $user->email }} • {{ $user->created_at->diffForHumans() }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @if($user->hasRole('admin'))
                                        bg-purple-100 text-purple-800
                                    @elseif($user->hasRole('agent'))
                                        bg-indigo-100 text-indigo-800
                                    @else
                                        bg-teal-100 text-teal-800
                                    @endif
                                ">
                            {{ ucfirst($user->roles->first()->name ?? 'Seeker') }}
                        </span>
                    </div>
                </div>
                @empty
                <p class="text-gray-500 text-sm">No recent users found.</p>
                @endforelse
            </div>
        </div>
    </div>
</div>