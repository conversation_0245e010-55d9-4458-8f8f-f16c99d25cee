<div>
    <div class="bg-white rounded-2xl shadow-sm p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ $is_editing ? 'Edit Property Sub-type' : 'Create Property Sub-type' }}</h2>

        @if (session()->has('message'))
            <flux:banner variant="success" class="mb-4">
                {{ session('message') }}
            </flux:banner>
        @endif

        <form wire:submit.prevent="{{ $is_editing ? 'updatePropertySubType' : 'savePropertySubType' }}">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:input id="name" wire:model.defer="name" label="Name" />
                    @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                <div>
                    <flux:select id="property_type_id" wire:model.defer="property_type_id" label="Property Type">
                        <option value="">Select a property type</option>
                        @foreach ($propertyTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </flux:select>
                    @error('property_type_id') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="mt-6">
                <flux:button type="submit" variant="primary" loading="true">
                    <span wire:loading.remove wire:target="{{ $is_editing ? 'updatePropertySubType' : 'savePropertySubType' }}">
                        {{ $is_editing ? 'Update' : 'Save' }}
                    </span>
                    <span wire:loading wire:target="{{ $is_editing ? 'updatePropertySubType' : 'savePropertySubType' }}">
                        {{ $is_editing ? 'Updating...' : 'Saving...' }}
                    </span>
                </flux:button>
            </div>
        </form>
    </div>

    <div class="bg-white rounded-2xl shadow-sm p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Property Sub-types</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property Type</th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Edit</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach ($propertySubTypes as $subType)
                        <tr wire:key="property-sub-type-{{ $subType->id }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $subType->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $subType->propertyType->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <flux:button wire:click="edit({{ $subType->id }})" size="sm" variant="ghost">
                                    Edit
                                </flux:button>
                                <flux:button wire:click="delete({{ $subType->id }})" wire:confirm="Are you sure you want to delete this property sub-type?" size="sm" variant="danger">
                                    Delete
                                </flux:button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $propertySubTypes->links() }}
        </div>
    </div>
</div>
