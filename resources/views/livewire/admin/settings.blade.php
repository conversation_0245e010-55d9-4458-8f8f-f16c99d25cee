<div>
    <div class="bg-white shadow-md rounded-lg p-8">
        <flux:heading size="xl">Admin Settings</flux:heading>
        <flux:subheading>Manage application settings and configurations</flux:subheading>

        <div class="mt-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button wire:click="setActiveTab('taxonomy')" 
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'taxonomy' ? 'border-indigo-500 text-indigo-600' : '' }}">
                        Taxonomy
                    </button>
                    <button wire:click="setActiveTab('general')" 
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'general' ? 'border-indigo-500 text-indigo-600' : '' }}">
                        General
                    </button>
                    <button wire:click="setActiveTab('security')" 
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'security' ? 'border-indigo-500 text-indigo-600' : '' }}">
                        Security
                    </button>
                    <button wire:click="setActiveTab('email')" 
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'email' ? 'border-indigo-500 text-indigo-600' : '' }}">
                        Email
                    </button>
                    <button wire:click="setActiveTab('advanced')" 
                            class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'advanced' ? 'border-indigo-500 text-indigo-600' : '' }}">
                        Advanced
                    </button>
                </nav>
            </div>

            <div class="mt-6">
                @if($activeTab === 'taxonomy')
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="lg">Taxonomy Settings</flux:heading>
                        <flux:text>Manage property types, sub-types, and amenities.</flux:text>
                        
                        <div class="mt-4 grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <livewire:admin.property-type-manager />
                                <div class="mt-6">
                                    <livewire:admin.property-sub-type-manager />
                                </div>
                            </div>
                            <div>
                                <livewire:admin.amenity-manager />
                            </div>
                        </div>
                    </div>
                @elseif($activeTab === 'general')
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="lg">General Settings</flux:heading>
                        <flux:text>Configure basic application settings.</flux:text>
                        
                        <div class="mt-4 grid grid-cols-1 gap-4">
                            <div>
                                <flux:input id="site_name" label="Site Name" placeholder="Enter site name" value="Lokus" />
                                <flux:text size="sm" variant="muted">The name of your application.</flux:text>
                            </div>
                            <div>
                                <flux:select id="default_language" label="Default Language">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                </flux:select>
                                <flux:text size="sm" variant="muted">The default language for your application.</flux:text>
                            </div>
                            <div>
                                <flux:select id="default_timezone" label="Default Timezone">
                                    <option value="UTC" selected>UTC</option>
                                    <option value="America/New_York">America/New York</option>
                                    <option value="Europe/London">Europe/London</option>
                                    <option value="Asia/Tokyo">Asia/Tokyo</option>
                                </flux:select>
                                <flux:text size="sm" variant="muted">The default timezone for your application.</flux:text>
                            </div>
                            <div class="flex items-center">
                                <flux:checkbox id="maintenance_mode" label="Maintenance Mode" />
                                <flux:text size="sm" variant="muted" class="ml-2">Enable maintenance mode to restrict access to the application.</flux:text>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <flux:button variant="primary">Save Changes</flux:button>
                        </div>
                    </div>
                @elseif($activeTab === 'security')
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="lg">Security Settings</flux:heading>
                        <flux:text>Configure security-related settings.</flux:text>
                        
                        <div class="mt-4 grid grid-cols-1 gap-4">
                            <div class="flex items-center">
                                <flux:checkbox id="two_factor_auth" label="Enable Two-Factor Authentication" checked />
                                <flux:text size="sm" variant="muted" class="ml-2">Require users to enable two-factor authentication for added security.</flux:text>
                            </div>
                            <div>
                                <flux:input id="session_timeout" label="Session Timeout (minutes)" type="number" value="30" />
                                <flux:text size="sm" variant="muted">The number of minutes before a user's session expires due to inactivity.</flux:text>
                            </div>
                            <div class="flex items-center">
                                <flux:checkbox id="password_rotation" label="Force Password Rotation" />
                                <flux:text size="sm" variant="muted" class="ml-2">Require users to change their passwords every 90 days.</flux:text>
                            </div>
                            <div>
                                <flux:input id="allowed_ips" label="Allowed IP Addresses (comma separated)" placeholder="e.g., 127.0.0.1, ***********" />
                                <flux:text size="sm" variant="muted">Restrict admin access to specific IP addresses. Leave blank to allow all.</flux:text>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <flux:button variant="primary">Save Changes</flux:button>
                        </div>
                    </div>
                @elseif($activeTab === 'email')
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="lg">Email Settings</flux:heading>
                        <flux:text>Configure email service settings for notifications and inquiries.</flux:text>
                        
                        <div class="mt-4 grid grid-cols-1 gap-4">
                            <div>
                                <flux:select id="mail_driver" label="Mail Driver">
                                    <option value="smtp" selected>SMTP</option>
                                    <option value="sendmail">Sendmail</option>
                                    <option value="mailgun">Mailgun</option>
                                    <option value="ses">SES</option>
                                </flux:select>
                                <flux:text size="sm" variant="muted">The driver to use for sending emails.</flux:text>
                            </div>
                            <div>
                                <flux:input id="smtp_host" label="SMTP Host" placeholder="smtp.example.com" />
                                <flux:text size="sm" variant="muted">The host for SMTP email service.</flux:text>
                            </div>
                            <div>
                                <flux:input id="smtp_port" label="SMTP Port" type="number" value="587" />
                                <flux:text size="sm" variant="muted">The port for SMTP email service.</flux:text>
                            </div>
                            <div>
                                <flux:input id="smtp_username" label="SMTP Username" placeholder="your-username" />
                                <flux:text size="sm" variant="muted">The username for SMTP authentication.</flux:text>
                            </div>
                            <div>
                                <flux:input id="smtp_password" label="SMTP Password" type="password" placeholder="your-password" />
                                <flux:text size="sm" variant="muted">The password for SMTP authentication.</flux:text>
                            </div>
                            <div>
                                <flux:input id="from_email" label="From Email Address" placeholder="<EMAIL>" value="<EMAIL>" />
                                <flux:text size="sm" variant="muted">The email address that emails are sent from.</flux:text>
                            </div>
                            <div>
                                <flux:input id="from_name" label="From Name" placeholder="Lokus Notifications" value="Lokus Notifications" />
                                <flux:text size="sm" variant="muted">The name that emails are sent from.</flux:text>
                            </div>
                            <div class="flex items-center">
                                <flux:checkbox id="email_notifications" label="Enable Email Notifications" checked />
                                <flux:text size="sm" variant="muted" class="ml-2">Send email notifications for user actions like registration and property inquiries.</flux:text>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <flux:button variant="primary">Save Changes</flux:button>
                            <flux:button variant="outline" class="ml-2">Test Email Configuration</flux:button>
                        </div>
                    </div>
                @elseif($activeTab === 'advanced')
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="lg">Advanced Settings</flux:heading>
                        <flux:text>Configure advanced application settings. Be cautious when modifying these settings.</flux:text>
                        
                        <div class="mt-4 grid grid-cols-1 gap-4">
                            <div class="flex items-center">
                                <flux:checkbox id="debug_mode" label="Enable Debug Mode" />
                                <flux:text size="sm" variant="muted" class="ml-2">Enable debug mode to display detailed error messages. Not recommended for production.</flux:text>
                            </div>
                            <div class="flex items-center">
                                <flux:checkbox id="log_queries" label="Log Database Queries" />
                                <flux:text size="sm" variant="muted" class="ml-2">Log all database queries for debugging purposes. May impact performance.</flux:text>
                            </div>
                            <div>
                                <flux:input id="cache_lifetime" label="Cache Lifetime (minutes)" type="number" value="60" />
                                <flux:text size="sm" variant="muted">The duration for which cached data is stored.</flux:text>
                            </div>
                            <div class="flex items-center">
                                <flux:checkbox id="auto_backup" label="Enable Automatic Backups" checked />
                                <flux:text size="sm" variant="muted" class="ml-2">Automatically backup the database on a scheduled basis.</flux:text>
                            </div>
                            <div>
                                <flux:select id="backup_frequency" label="Backup Frequency">
                                    <option value="daily" selected>Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </flux:select>
                                <flux:text size="sm" variant="muted">How often to perform automatic backups.</flux:text>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <flux:button variant="primary">Save Changes</flux:button>
                            <flux:button variant="danger" class="ml-2">Clear Cache</flux:button>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
