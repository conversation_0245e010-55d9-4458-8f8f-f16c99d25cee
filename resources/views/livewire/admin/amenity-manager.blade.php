<div>
    <div class="bg-white rounded-2xl shadow-sm p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ $is_editing ? 'Edit Amenity' : 'Create Amenity' }}</h2>

        @if (session()->has('message'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('message') }}</span>
            </div>
        @endif

        <form wire:submit.prevent="{{ $is_editing ? 'updateAmenity' : 'saveAmenity' }}">
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <flux:input id="name" wire:model.defer="name" label="Name" />
                    @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                    {{ $is_editing ? 'Update' : 'Save' }}
                </button>
            </div>
        </form>
    </div>

    <div class="bg-white rounded-2xl shadow-sm p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Amenities</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Edit</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach ($amenities as $amenity)
                        <tr wire:key="amenity-{{ $amenity->id }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $amenity->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button wire:click="edit({{ $amenity->id }})" class="text-blue-600 hover:text-blue-900">Edit</button>
                                <button wire:click="delete({{ $amenity->id }})" wire:confirm="Are you sure you want to delete this amenity?" class="text-red-600 hover:text-red-900 ml-4">Delete</button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $amenities->links() }}
        </div>
    </div>
</div>
