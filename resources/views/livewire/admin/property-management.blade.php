<div>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Property Management</h1>
        <p class="mt-2 text-gray-600">Manage all property listings and their status</p>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        <!-- Search and Filters -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Search & Filter</flux:heading>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <flux:field>
                        <flux:label>Keywords</flux:label>
                        <flux:input wire:model.live.debounce.500ms="search" placeholder="Search properties..." />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Property Type</flux:label>
                        <flux:select wire:model.live="propertyTypeFilter" placeholder="All Types">
                            <option value="">All Types</option>
                            @foreach ($propertyTypes as $type)
                                <option value="{{ $type->id }}">{{ $type->name }}</option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Property Sub-Type</flux:label>
                        <flux:select wire:model.live="propertySubTypeFilter" placeholder="All Sub-Types" :disabled="!$propertyTypeFilter">
                            <option value="">All Sub-Types</option>
                            @foreach ($propertySubTypes as $subType)
                                <option value="{{ $subType->id }}">{{ $subType->name }}</option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Status</flux:label>
                        <flux:select wire:model.live="statusFilter" placeholder="All Statuses">
                            <option value="">All Statuses</option>
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                            <option value="sold">Sold</option>
                            <option value="rented">Rented</option>
                            <option value="under_offer">Under Offer</option>
                        </flux:select>
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Properties Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('id')">ID <i class="fas {{ $sortField == 'id' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('title')">Title <i class="fas {{ $sortField == 'title' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('price')">Price <i class="fas {{ $sortField == 'price' ? ($sortDirection == 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort' }}"></i></th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach ($properties as $property)
                            <tr wire:key="admin-property-{{ $property->id }}">
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->title }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->propertySubType->propertyType->name ?? 'N/A' }} ({{ $property->listing_type }})</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->currency }} {{ number_format($property->price) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <flux:badge :variant="$property->status == 'published' ? 'success' : ($property->status == 'draft' ? 'warning' : 'secondary')">
                                        {{ ucfirst(str_replace('_', ' ', $property->status)) }}
                                    </flux:badge>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <!-- Edit button with pencil icon -->
                                        <a href="{{ route('admin.properties.edit', $property->id) }}" class="p-1 text-blue-300 hover:text-blue-600">
                                            <flux:icon.pencil-square />
                                        </a>
                                        
                                        <!-- Featured button with star icon -->
                                        <flux:button wire:click="toggleFeatured({{ $property->id }})" size="sm" :variant="$property->is_featured ? 'warning' : 'ghost'" loading="true">
                                            <flux:icon.star />
                                            <span wire:loading wire:target="toggleFeatured({{ $property->id }})" class="ml-1">...</span>
                                        </flux:button>

                                        <!-- Delete button with trash icon -->
                                        <flux:button wire:click="deleteProperty({{ $property->id }})" wire:confirm="Are you sure you want to delete this property?" size="sm" variant="danger" loading="true">
                                            <flux:icon.trash />
                                            <span wire:loading wire:target="deleteProperty({{ $property->id }})" class="ml-1">...</span>
                                        </flux:button>
                                        
                                        <!-- Status dropdown -->
                                        <flux:select wire:change="updateStatus({{ $property->id }}, $event.target.value)" size="sm">
                                            <option value="draft" {{ $property->status == 'draft' ? 'selected' : '' }}>Draft</option>
                                            <option value="published" {{ $property->status == 'published' ? 'selected' : '' }}>Publish</option>
                                            <option value="sold" {{ $property->status == 'sold' ? 'selected' : '' }}>Sold</option>
                                            <option value="rented" {{ $property->status == 'rented' ? 'selected' : '' }}>Rented</option>
                                            <option value="under_offer" {{ $property->status == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                                        </flux:select>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="p-6 border-t border-gray-200">
                {{ $properties->links() }}
            </div>
        </div>
    </div>
</div>
