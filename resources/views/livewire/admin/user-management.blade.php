<div>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
        <p class="mt-2 text-gray-600">Manage user accounts and roles</p>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        <!-- Search and Filters -->
        <div class="bg-white p-6 rounded-lg shadow border">
        <div class="mb-6">
            <flux:heading size="xl">Search & Filter</flux:heading>
        </div>

        <form wire:submit.prevent="search" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <flux:field>
                        <flux:label>Name or Email</flux:label>
                        <flux:input type="text" wire:model.lazy="search" placeholder="Search users..." />
                    </flux:field>
                </div>

                <flux:field>
                    <flux:label>Filter by Role</flux:label>
                    <flux:select wire:model.live="roleFilter">
                        <option value="">All Roles</option>
                        <option value="seeker">Seeker</option>
                        <option value="agent">Agent</option>
                        <option value="admin">Admin</option>
                    </flux:select>
                </flux:field>

                <flux:field>
                    <flux:label>Filter by Status</flux:label>
                    <flux:select wire:model.live="statusFilter">
                        <option value="">All Statuses</option>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </flux:select>
                </flux:field>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white rounded-lg shadow border overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <flux:heading size="xl">All Users</flux:heading>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('name')">
                                Name
                                @if ($sortField === 'name')
                                    <span class="ml-1">{{ $sortDirection === 'asc' ? '▲' : '▼' }}</span>
                                @endif
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('email')">
                                Email
                                @if ($sortField === 'email')
                                    <span class="ml-1">{{ $sortDirection === 'asc' ? '▲' : '▼' }}</span>
                                @endif
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse ($users as $user)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $user->email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <flux:select wire:change="updateRole({{ $user->id }}, $event.target.value)" size="sm">
                                        <option value="seeker" {{ $user->hasRole('seeker') ? 'selected' : '' }}>Seeker</option>
                                        <option value="agent" {{ $user->hasRole('agent') ? 'selected' : '' }}>Agent</option>
                                        <option value="admin" {{ $user->hasRole('admin') ? 'selected' : '' }}>Admin</option>
                                    </flux:select>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <flux:badge :variant="$user->is_active ? 'success' : 'danger'">
                                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                                    </flux:badge>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <flux:button wire:click="toggleActive({{ $user->id }})" size="sm" :variant="$user->is_active ? 'danger' : 'primary'">
                                        {{ $user->is_active ? 'Deactivate' : 'Activate' }}
                                    </flux:button>
                                    <flux:button wire:click="deleteUser({{ $user->id }})" wire:confirm="Are you sure you want to delete this user?" size="sm" variant="danger">
                                        Delete
                                    </flux:button>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">No users found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="p-6">
                {{ $users->links() }}
            </div>
        </div>
    </div>
</div>
