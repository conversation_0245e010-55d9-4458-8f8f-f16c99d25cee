<div class="mt-6">
    <h2 class="text-xl font-bold text-gray-900 mb-4">Favorite Properties</h2>
    
    @if($properties->isEmpty())
        <p class="text-gray-600">You haven't favorited any properties yet.</p>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @foreach($properties as $property)
            <div class="border rounded-lg p-4 flex items-center gap-4" wire:key="favorite-{{ $property->id }}">
                @if(!empty($property->images) && is_array($property->images) && count($property->images) > 0)
                    <img src="{{ Storage::url($property->images[0]) }}" alt="{{ $property->title }}" class="w-16 h-16 object-cover rounded">
                @else
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16"></div>
                @endif
                <div>
                    <a href="{{ route('properties.show', $property) }}" class="font-medium text-blue-600 hover:underline">
                        {{ $property->title }}
                    </a>
                    <p class="text-sm text-gray-600">${{ number_format($property->price) }} {{ $property->listing_type === 'for_rent' ? '/month' : '' }}</p>
                    <p class="text-sm text-gray-500">{{ $property->city }}, {{ $property->state_region }}</p>
                </div>
            </div>
            @endforeach
        </div>
    @endif
</div>
