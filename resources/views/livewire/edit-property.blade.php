<div>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

    @if (session()->has('message'))
        <div class="p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg" role="alert">
            {{ session('message') }}
        </div>
    @endif

    <div class="bg-white shadow-md rounded-lg p-8">
        <form wire:submit="update">
            <!-- Property Taxonomy -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.building-office class="mr-2 size-6" />
                    Property Classification
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:select wire:model.live="property_type_id" id="property_type_id" label="Property Type" class="mt-1 block w-full">
                            <option value="">Select Property Type</option>
                            @foreach($property_types as $type)
                                <option value="{{ $type->name }}" @selected($type->id == $property_type_id)>
                                    {{ $type->name }}
                                </option>
                            @endforeach
                        </flux:select>
                        @error('property_type_id') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:select wire:model="property_sub_type_id" id="property_sub_type_id" label="Property Sub-Type" class="mt-1 block w-full" :disabled="!$property_type_id">
                            <option value="">Select Sub-Type</option>
                            @foreach($property_sub_types as $sub_type)
                                <option value="{{ $sub_type->name }}" @selected($sub_type->id == $property_sub_type_id)>
                                    {{ $sub_type->name }}
                                </option>
                            @endforeach
                        </flux:select>
                        @error('property_sub_type_id') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                </div>
            </div>

            <!-- Basic Info -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.document-text class="mr-2 size-6" />
                    Basic Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:input wire:model="title" id="title" type="text" label="Title" class="mt-1 block w-full" placeholder="e.g., Spacious 3-Bedroom Apartment" />
                        @error('title') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:select wire:model="listing_type" id="listing_type" label="Listing Type" class="mt-1 block w-full">
                            <option value="for_sale">For Sale</option>
                            <option value="for_rent">For Rent</option>
                        </flux:select>
                        @error('listing_type') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div class="md:col-span-2">
                        <flux:textarea wire:model="description" id="description" label="Description" rows="5" class="mt-1 block w-full" placeholder="Describe your property..." />
                        @error('description') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="price" id="price" type="number" step="0.01" label="Price" class="mt-1 block w-full" placeholder="e.g., 250000" />
                        @error('price') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="currency" id="currency" type="text" label="Currency" class="mt-1 block w-full" placeholder="e.g., USD" />
                        @error('currency') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                </div>
            </div>

            <!-- Property Features -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.layout-grid class="mr-2 size-6" />
                    Property Features
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <flux:input wire:model="bedrooms" id="bedrooms" type="number" label="Bedrooms" class="mt-1 block w-full" placeholder="e.g., 3" />
                        @error('bedrooms') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="bathrooms" id="bathrooms" type="number" label="Bathrooms" class="mt-1 block w-full" placeholder="e.g., 2" />
                        @error('bathrooms') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="square_footage" id="square_footage" type="number" label="Square Footage" class="mt-1 block w-full" placeholder="e.g., 1500" />
                        @error('square_footage') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                </div>
            </div>

            <!-- Amenities -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.check-circle class="mr-2 size-6" />
                    Amenities
                </h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    @foreach($amenities as $amenity)
                        <div>
                            <label for="amenity_{{ $amenity->id }}" class="flex items-center">
                                <input wire:model="selected_amenities" id="amenity_{{ $amenity->id }}" type="checkbox" value="{{ $amenity->id }}" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-600">{{ $amenity->name }}</span>
                            </label>
                        </div>
                    @endforeach
                </div>
                @error('selected_amenities') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <!-- Location -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.home class="mr-2 size-6" />
                    Location Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:input wire:model="address_line_1" id="address_line_1" type="text" label="Address Line 1" class="mt-1 block w-full" placeholder="e.g., 123 Main St" />
                        @error('address_line_1') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="city" id="city" type="text" label="City" class="mt-1 block w-full" placeholder="e.g., New York" />
                        @error('city') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="state_region" id="state_region" type="text" label="State/Region" class="mt-1 block w-full" placeholder="e.g., NY" />
                        @error('state_region') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="zip_code" id="zip_code" type="text" label="Zip Code" class="mt-1 block w-full" placeholder="e.g., 10001" />
                        @error('zip_code') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="latitude" id="latitude" type="number" step="any" label="Latitude" class="mt-1 block w-full" placeholder="e.g., 34.052235" />
                        @error('latitude') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                    <div>
                        <flux:input wire:model="longitude" id="longitude" type="number" step="any" label="Longitude" class="mt-1 block w-full" placeholder="e.g., -118.243683" />
                        @error('longitude') <flux:error>{{ $message }}</flux:error> @enderror
                    </div>
                </div>
                <div class="mt-4">
                    <div id="mapEdit" style="height: 300px;" class="mt-1 rounded-md border border-gray-300 shadow-sm" wire:ignore></div>
                </div>
            </div>

            <!-- Image Upload -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.plus-circle class="mr-2 size-6" />
                    Property Images
                </h3>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-indigo-500 transition duration-200" id="dropArea">
                    <input type="file" wire:model="images" multiple accept="image/*" class="hidden" id="fileInput" />
                    <div id="noImages" class="text-gray-500">
                        <p class="text-lg mb-2">Drag and drop images here or click to browse</p>
                        <p class="text-sm">Supports multiple images (JPEG, PNG, GIF)</p>
                    </div>
                    <div id="previewContainer" class="grid grid-cols-2 md:grid-cols-4 gap-4 hidden">
                        <!-- Previews will be dynamically inserted here -->
                    </div>
                </div>
                <div wire:loading wire:target="images" class="mt-2 text-sm text-gray-500">Uploading...</div>
                @error('images.*') <flux:error>{{ $message }}</flux:error> @enderror
                @if ($images)
                    <div class="mt-4">
                        <h4 class="text-md font-medium text-gray-700">Current Images</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                            @foreach ($images as $image)
                                <img src="{{ asset('/storage/' . $image) }}" alt="Property Image" class="w-full h-24 object-cover rounded-md border border-gray-200">
                            @endforeach
                        </div>
                        <p class="text-sm text-gray-500 mt-2">New images will be added to the existing ones.</p>
                    </div>
                @endif
            </div>

            <!-- Status -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                    <flux:icon.check-circle class="mr-2 size-6" />
                    Status
                </h3>
                <div>
                        <flux:select wire:model="status" id="status" label="Status" class="mt-1 block w-full">
                        <option value="draft">Draft</option>
                        <option value="published">Published</option>
                        <option value="sold">Sold</option>
                        <option value="rented">Rented</option>
                        <option value="under_offer">Under Offer</option>
                    </flux:select>
                    @error('status') <flux:error>{{ $message }}</flux:error> @enderror
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex items-center justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150" wire:loading.attr="disabled">
                    <flux:icon.check-circle class="mr-2 size-4" />
                    Update Listing
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:init', () => {
        const component = Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id'));
        let map = L.map('mapEdit').setView([0, 0], 2);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
        let marker;

        map.on('click', function(e) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            component.set('latitude', lat);
            component.set('longitude', lng);
        });

        component.on('location-updated', (event) => {
            const lat = event.lat;
            const lng = event.lng;
            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker([lat, lng]).addTo(map);
            map.setView([lat, lng], 13);
        });

        // Watch for changes in the component's lat/lng properties
        let initialLat = component.get('latitude');
        let initialLng = component.get('longitude');
        if(initialLat && initialLng) {
            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker([initialLat, initialLng]).addTo(map);
            map.setView([initialLat, initialLng], 13);
        }
    });

    // Images section
    document.addEventListener('DOMContentLoaded', () => {
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('fileInput');
        const noImages = document.getElementById('noImages');
        const previewContainer = document.getElementById('previewContainer');
        let files = [];

        // Handle click to browse
        dropArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Handle file selection via input
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        // Handle drag and drop events
        dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropArea.classList.add('border-indigo-500', 'bg-indigo-50');
        });

        dropArea.addEventListener('dragleave', () => {
            dropArea.classList.remove('border-indigo-500', 'bg-indigo-50');
        });

        dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dropArea.classList.remove('border-indigo-500', 'bg-indigo-50');
            const droppedFiles = e.dataTransfer.files;
            handleFiles(droppedFiles);
            fileInput.files = droppedFiles; // Assign dropped files to hidden input for Livewire
        });

        function handleFiles(selectedFiles) {
            if (selectedFiles.length > 0) {
                noImages.classList.add('hidden');
                previewContainer.classList.remove('hidden');
                previewContainer.innerHTML = ''; // Clear existing previews
                files = Array.from(selectedFiles).filter(file => file.type.startsWith('image/'));
                
                files.forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        const imgContainer = document.createElement('div');
                        imgContainer.classList.add('relative', 'group');
                        
                        const img = document.createElement('img');
                        img.src = event.target.result;
                        img.classList.add('w-full', 'h-24', 'object-cover', 'rounded-md', 'border', 'border-gray-200');
                        
                        const removeBtn = document.createElement('button');
                        removeBtn.classList.add('absolute', 'top-1', 'right-1', 'bg-red-500', 'text-white', 'rounded-full', 'w-5', 'h-5', 'flex', 'items-center', 'justify-center', 'opacity-0', 'group-hover:opacity-100', 'transition', 'duration-200');
                        removeBtn.innerHTML = '×';
                        removeBtn.onclick = () => removeImage(index);
                        
                        imgContainer.appendChild(img);
                        imgContainer.appendChild(removeBtn);
                        previewContainer.appendChild(imgContainer);
                    };
                    reader.readAsDataURL(file);
                });
            } else {
                noImages.classList.remove('hidden');
                previewContainer.classList.add('hidden');
            }
        }

        function removeImage(index) {
            files.splice(index, 1);
            const dt = new DataTransfer();
            files.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
            handleFiles(files); // Refresh previews
        }
    });
</script>
@endpush

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
@stack('scripts')
