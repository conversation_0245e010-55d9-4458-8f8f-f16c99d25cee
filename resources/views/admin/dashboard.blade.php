<x-layouts.app title="Admin Dashboard">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="mt-2 text-gray-600">Manage users, properties, and system settings</p>
    </div>

    <!-- Dashboard Content -->
    <div class="space-y-8">
        <!-- Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-blue-600">{{ $totalUsers }}</flux:heading>
                        <flux:subheading>Total Users</flux:subheading>
                        <div class="text-xs text-gray-500 mt-1">
                            {{ $activeUsers }} active, {{ $inactiveUsers }} inactive
                        </div>
                    </div>
                    <flux:icon.users class="size-8 text-blue-500" />
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-green-600">{{ $totalProperties }}</flux:heading>
                        <flux:subheading>Total Properties</flux:subheading>
                        <div class="text-xs text-gray-500 mt-1">
                            Avg: ${{ number_format($averagePrice ?? 0, 0) }}
                        </div>
                    </div>
                    <flux:icon.building-office class="size-8 text-green-500" />
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-emerald-600">{{ $publishedProperties }}</flux:heading>
                        <flux:subheading>Published Properties</flux:subheading>
                        <div class="text-xs text-gray-500 mt-1">
                            {{ $soldProperties }} sold, {{ $rentedProperties }} rented
                        </div>
                    </div>
                    <flux:icon.check-circle class="size-8 text-emerald-500" />
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-amber-600">{{ $pendingProperties }}</flux:heading>
                        <flux:subheading>Pending Approval</flux:subheading>
                        <div class="text-xs text-gray-500 mt-1">
                            {{ $underOfferProperties }} under offer
                        </div>
                    </div>
                    <flux:icon.clock class="size-8 text-amber-500" />
                </div>
            </div>
        </div>

        <!-- User Role Breakdown -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-purple-600">{{ $adminUsers }}</flux:heading>
                        <flux:subheading>Admin Users</flux:subheading>
                    </div>
                    <flux:icon.shield class="size-8 text-purple-500" />
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-indigo-600">{{ $agentUsers }}</flux:heading>
                        <flux:subheading>Agents</flux:subheading>
                    </div>
                    <flux:icon.building-office class="size-8 text-indigo-500" />
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="xl" class="text-teal-600">{{ $seekerUsers }}</flux:heading>
                        <flux:subheading>Property Seekers</flux:subheading>
                    </div>
                    <flux:icon.users class="size-8 text-teal-500" />
                </div>
            </div>
        </div>

        <!-- Property Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Property Types Breakdown -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-4">
                    <flux:heading size="xl">Property Types</flux:heading>
                    <flux:subheading>Distribution by property type</flux:subheading>
                </div>
                <div class="space-y-3">
                    @foreach($propertyTypes as $type => $count)
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700 capitalize">{{ str_replace('_', ' ', $type) }}</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: '{{ $totalProperties > 0 ? ($count / $totalProperties) * 100 : 0 }}%'"></div>
                                </div>
                                <span class="text-sm text-gray-600">{{ $count }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Top Cities -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-4">
                    <flux:heading size="xl">Top Cities</flux:heading>
                    <flux:subheading>Properties by location</flux:subheading>
                </div>
                <div class="space-y-3">
                    @foreach($topCities as $city => $count)
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">{{ $city }}</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: '{{ $totalProperties > 0 ? ($count / $totalProperties) * 100 : 0 }}%'"></div>
                                </div>
                                <span class="text-sm text-gray-600">{{ $count }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Properties -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-4">
                    <flux:heading size="xl">Recent Properties</flux:heading>
                    <flux:subheading>Latest property listings</flux:subheading>
                </div>
                <div class="space-y-4">
                    @forelse($recentProperties as $property)
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <flux:icon.building-office class="size-6 text-gray-400" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ $property->title }}</p>
                                <p class="text-xs text-gray-500">by {{ $property->user->name }} • {{ $property->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $property->status === 'published' ? 'bg-green-100 text-green-800' :
                                       ($property->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ ucfirst($property->status) }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-sm">No recent properties found.</p>
                    @endforelse
                </div>
            </div>

            <!-- Recent Users -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-4">
                    <flux:heading size="xl">Recent Users</flux:heading>
                    <flux:subheading>Latest user registrations</flux:subheading>
                </div>
                <div class="space-y-4">
                    @forelse($recentUsers as $user)
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    {{ $user->initials() }}
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ $user->name }}</p>
                                <p class="text-xs text-gray-500">{{ $user->email }} • {{ $user->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @if($user->hasRole('admin'))
                                        bg-purple-100 text-purple-800
                                    @elseif($user->hasRole('agent'))
                                        bg-indigo-100 text-indigo-800
                                    @else
                                        bg-teal-100 text-teal-800
                                    @endif
                                ">
                                    {{ ucfirst($user->roles->first()->name ?? 'Seeker') }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-sm">No recent users found.</p>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Taxonomy Management -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Taxonomy Management</flux:heading>
                <flux:subheading>Manage property types, sub-types, and amenities</flux:subheading>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div>
                    <livewire:admin.property-type-manager />
                </div>
                <div>
                    <livewire:admin.property-sub-type-manager />
                </div>
                <div>
                    <livewire:admin.amenity-manager />
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Quick Actions</flux:heading>
                <flux:subheading>Common administrative tasks</flux:subheading>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <flux:button :href="route('admin.users.index')" variant="primary" class="justify-start py-3" wire:navigate>
                    <flux:icon.users class="size-5" />
                    Manage Users
                </flux:button>

                <flux:button :href="route('admin.properties.index')" variant="primary" class="justify-start py-3" wire:navigate>
                    <flux:icon.building-office class="size-5" />
                    Manage Properties
                </flux:button>

                <flux:button :href="route('properties.index')" variant="outline" class="justify-start py-3" wire:navigate>
                    <flux:icon.magnifying-glass class="size-5" />
                    View Public Listings
                </flux:button>

                <flux:button :href="route('admin.users.index', ['roleFilter' => 'agent'])" variant="outline" class="justify-start py-3" wire:navigate>
                    <flux:icon.plus-circle class="size-5" />
                    View Agents
                </flux:button>
                
                <flux:button href="{{ route('admin.favorites') }}" variant="primary" class="justify-start py-3" wire:navigate>
                    <flux:icon.heart class="size-5" />
                    Manage Favorites
                </flux:button>
            </div>
        </div>
    </div>
</x-layouts.app>
