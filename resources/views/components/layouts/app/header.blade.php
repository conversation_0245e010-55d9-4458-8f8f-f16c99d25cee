<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white">
        <flux:header container class="border-b border-zinc-200 bg-zinc-50">
            <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

            <a href="{{ route('dashboard') }}" class="ms-2 me-5 flex items-center space-x-2 rtl:space-x-reverse lg:ms-0" wire:navigate>
                <x-app-logo />
            </a>

            <flux:navbar class="-mb-px max-lg:hidden">
                <flux:navbar.item icon="layout-grid" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate>
                    {{ __('Dashboard') }}
                </flux:navbar.item>
                <flux:navbar.item icon="home" :href="route('properties.index')" :current="request()->routeIs('properties.index')" wire:navigate>
                    {{ __('Properties') }}
                </flux:navbar.item>
                @auth
                    @if (auth()->user()->hasRole('agent'))
                        <flux:navbar.item icon="building-office" :href="route('agent.properties.index')" :current="request()->routeIs('agent.properties.*')" wire:navigate>
                            {{ __('My Properties') }}
                        </flux:navbar.item>
                        <flux:navbar.item icon="plus-circle" :href="route('agent.properties.create')" :current="request()->routeIs('agent.properties.create')" wire:navigate>
                            {{ __('Create Listing') }}
                        </flux:navbar.item>
                    @endif
                    @if (auth()->user()->hasRole('admin'))
                        <flux:navbar.item icon="shield" :href="route('admin.dashboard')" :current="request()->routeIs('admin.*')" wire:navigate>
                            {{ __('Admin Dashboard') }}
                        </flux:navbar.item>
                        <flux:navbar.item icon="users" :href="route('admin.users.index')" :current="request()->routeIs('admin.users.*')" wire:navigate>
                            {{ __('Users') }}
                        </flux:navbar.item>
                        <flux:navbar.item icon="building-office" :href="route('admin.properties.index')" :current="request()->routeIs('admin.properties.*')" wire:navigate>
                            {{ __('Admin Properties') }}
                        </flux:navbar.item>
                    @endif
                @endauth
            </flux:navbar>

            <flux:spacer />

            <flux:navbar class="me-1.5 space-x-0.5 rtl:space-x-reverse py-0!">
                <flux:tooltip :content="__('Search Properties')" position="bottom">
                    <flux:navbar.item class="!h-10 [&>div>svg]:size-5" icon="magnifying-glass" :href="route('properties.index')" wire:navigate :label="__('Search')" />
                </flux:tooltip>
                <flux:tooltip :content="__('Public Homepage')" position="bottom">
                    <flux:navbar.item
                        class="h-10 max-lg:hidden [&>div>svg]:size-5"
                        icon="home"
                        :href="route('home')"
                        wire:navigate
                        :label="__('Homepage')"
                    />
                </flux:tooltip>
                <flux:tooltip :content="__('Help & Support')" position="bottom">
                    <flux:navbar.item
                        class="h-10 max-lg:hidden [&>div>svg]:size-5"
                        icon="question-mark-circle"
                        href="mailto:<EMAIL>"
                        label="Help"
                    />
                </flux:tooltip>
            </flux:navbar>

            <!-- Desktop User Menu -->
            @auth
                <flux:dropdown position="top" align="end">
                    <flux:profile
                        class="cursor-pointer"
                        :initials="auth()->user()->initials()"
                    />

                    <flux:menu>
                        <flux:menu.radio.group>
                            <div class="p-0 text-sm font-normal">
                                <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                    <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                        <span
                                            class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black"
                                        >
                                            {{ auth()->user()->initials() }}
                                        </span>
                                    </span>

                                    <div class="grid flex-1 text-start text-sm leading-tight">
                                        <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                        <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                    </div>
                                </div>
                            </div>
                        </flux:menu.radio.group>

                        <flux:menu.separator />

                        <flux:menu.radio.group>
                            <flux:menu.item :href="route('profile.edit')" icon="user" wire:navigate>{{ __('Profile') }}</flux:menu.item>
                            <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>

                            @if (auth()->user()->hasRole('agent'))
                                <flux:menu.separator />
                                <flux:menu.item :href="route('agent.properties.index')" icon="building-office" wire:navigate>{{ __('My Properties') }}</flux:menu.item>
                                <flux:menu.item :href="route('agent.properties.create')" icon="plus-circle" wire:navigate>{{ __('Create Listing') }}</flux:menu.item>
                            @endif

                            @if (auth()->user()->hasRole('admin'))
                                <flux:menu.separator />
                                <flux:menu.item :href="route('admin.dashboard')" icon="shield" wire:navigate>{{ __('Admin Dashboard') }}</flux:menu.item>
                                <flux:menu.item :href="route('admin.users.index')" icon="users" wire:navigate>{{ __('User Management') }}</flux:menu.item>
                                <flux:menu.item :href="route('admin.properties.index')" icon="building-office" wire:navigate>{{ __('Property Management') }}</flux:menu.item>
                            @endif
                        </flux:menu.radio.group>

                        <flux:menu.separator />

                        <form method="POST" action="{{ route('logout') }}" class="w-full">
                            @csrf
                            <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                {{ __('Log Out') }}
                            </flux:menu.item>
                        </form>
                    </flux:menu>
                </flux:dropdown>
            @else
                <flux:navbar class="me-1.5 space-x-0.5 rtl:space-x-reverse py-0!">
                    <flux:navbar.item :href="route('login')" wire:navigate>{{ __('Log in') }}</flux:navbar.item>
                    <flux:navbar.item :href="route('register')" wire:navigate>{{ __('Register') }}</flux:navbar.item>
                </flux:navbar>
            @endauth
        </flux:header>

        <!-- Mobile Menu -->
        <flux:sidebar stashable sticky class="lg:hidden border-e border-zinc-200 bg-zinc-50">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

            <a href="{{ route('dashboard') }}" class="ms-1 flex items-center space-x-2 rtl:space-x-reverse" wire:navigate>
                <x-app-logo />
            </a>

            <flux:navlist variant="outline">
                <flux:navlist.group :heading="__('Platform')">
                    <flux:navlist.item icon="layout-grid" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate>
                        {{ __('Dashboard') }}
                    </flux:navlist.item>
                    <flux:navlist.item icon="home" :href="route('properties.index')" :current="request()->routeIs('properties.index')" wire:navigate>
                        {{ __('Properties') }}
                    </flux:navlist.item>
                    @auth
                        @if (auth()->user()->hasRole('agent'))
                            <flux:navlist.item icon="building-office" :href="route('agent.properties.index')" :current="request()->routeIs('agent.properties.*')" wire:navigate>
                                {{ __('My Properties') }}
                            </flux:navlist.item>
                            <flux:navlist.item icon="plus-circle" :href="route('agent.properties.create')" :current="request()->routeIs('agent.properties.create')" wire:navigate>
                                {{ __('Create Listing') }}
                            </flux:navlist.item>
                        @endif
                        @if (auth()->user()->hasRole('admin'))
                            <flux:navlist.item icon="shield" :href="route('admin.dashboard')" :current="request()->routeIs('admin.*')" wire:navigate>
                                {{ __('Admin Dashboard') }}
                            </flux:navlist.item>
                            <flux:navlist.item icon="users" :href="route('admin.users.index')" :current="request()->routeIs('admin.users.*')" wire:navigate>
                                {{ __('User Management') }}
                            </flux:navlist.item>
                            <flux:navlist.item icon="building-office" :href="route('admin.properties.index')" :current="request()->routeIs('admin.properties.*')" wire:navigate>
                                {{ __('Property Management') }}
                            </flux:navlist.item>
                        @endif
                    @endauth
                </flux:navlist.group>
            </flux:navlist>

            <flux:spacer />

            <flux:navlist variant="outline">
                <flux:navlist.item icon="home" :href="route('home')" wire:navigate>
                    {{ __('Public Homepage') }}
                </flux:navlist.item>

                <flux:navlist.item icon="question-mark-circle" href="mailto:<EMAIL>">
                    {{ __('Help & Support') }}
                </flux:navlist.item>
            </flux:navlist>
        </flux:sidebar>

        {{ $slot }}

        @fluxScripts
    </body>
</html>
