{"appName": "<PERSON><PERSON>", "version": "2.0", "description": "A web application for listing, searching, and managing real estate properties. It features a robust, scalable architecture with distinct user roles managed by Spatie/laravel-permission, a detailed property taxonomy, and advanced management capabilities.", "technologies": {"backend": "PHP / Laravel", "frontend": "Blade, Livewire, Tailwind CSS, JavaScript, Vite", "database": "SQL (using SQLite for development, PostgreSQL/MySQL for production)", "packages": ["spatie/laravel-permission", "spatie/laravel-medialibrary"], "testing": "PHPUnit, Pest"}, "database_schema": {"users": "Stores user data. Manages roles via a many-to-many relationship with the `roles` table, provided by <PERSON><PERSON>/<PERSON>ravel-permission.", "roles": "Defines user roles (e.g., Seeker, Agent, Admin). Provided by <PERSON><PERSON>/laravel-permission.", "permissions": "Defines granular permissions, linked to roles. Provided by <PERSON><PERSON>/laravel-permission.", "model_has_roles": "Pivot table for assigning roles to users. Provided by Spa<PERSON>/laravel-permission.", "role_has_permissions": "Pivot table for assigning permissions to roles. Provided by <PERSON><PERSON>/laravel-permission.", "properties": "Core table for property listings. Contains dedicated columns for key attributes to ensure query performance.", "property_types": "Top-level categories for properties (e.g., RESIDENTIAL, COMMERCIAL).", "property_sub_types": "Detailed property classifications (e.g., APARTMENT, BUNGALOW), linked to a parent `property_type`.", "amenities": "A predefined list of all possible amenities (e.g., Swimming Pool, Gym).", "amenity_property": "Pivot table for the many-to-many relationship between properties and amenities.", "media": "Stores paths and metadata for user-uploaded images and videos, linked to properties, managed by Spatie/laravel-medialibrary.", "favorites": "Tracks which users have favorited which properties."}, "features": [{"name": "User Authentication & Role Management", "description": "Handles user registration, login, and session management. Manages user access and capabilities using a role-based permission system powered by Spatie/laravel-permission, allowing for flexible assignment of roles like 'Admin', 'Agent', and 'Seeker'.", "keyFiles": ["app/Models/User.php", "config/permission.php", "database/migrations/*_create_permission_tables.php", "database/migrations/*_remove_role_from_users_table.php", "database/seeders/RolesAndPermissionsSeeder.php", "database/seeders/UserRoleSeeder.php", "app/Http/Middleware/RoleMiddleware.php", "app/Livewire/Admin/UserManagement.php"]}, {"name": "Property Management", "description": "Core functionality for CRUD operations on properties. Utilizes a structured data model with dedicated columns for `bedrooms`, `bathrooms`, `square_footage`, etc., and relationships for taxonomy and amenities. Images are managed via Spatie Media Library.", "keyModels": ["app/Models/Property.php", "app/Models/PropertyType.php", "app/Models/PropertySubType.php", "app/Models/Amenity.php"], "keyEnums": ["app/Enums/PropertyStatus.php"], "keyFiles": ["app/Livewire/CreateProperty.php", "app/Http/Controllers/PropertyController.php", "resources/views/properties/show.blade.php"]}, {"name": "Property Search and Discovery", "description": "A public-facing feature allowing visitors to search and filter listings based on structured data like property type, sub-type, number of bedrooms, amenities, and location. This results in significantly faster and more powerful search queries.", "keyFiles": ["app/Livewire/PropertySearch.php", "resources/views/livewire/property-search.blade.php"]}, {"name": "User Profiles", "description": "Allows users to view and update their profile information. Profile structure may vary based on user role (e.g., an 'Agent' profile may have additional fields).", "keyFiles": ["app/Http/Controllers/ProfileController.php"]}, {"name": "Favorites System", "description": "Enables authenticated users to save properties to a personal list of favorites for easy access.", "keyFiles": ["app/Livewire/FavoriteButton.php"]}, {"name": "Admin Dashboard", "description": "A restricted backend for administrators to manage users (including role assignment), properties, and site-wide settings. This now includes dedicated UI sections for managing property types, sub-types, and amenities, migrated to Livewire components for enhanced interactivity. Access is controlled via the 'Admin' role.", "keyFiles": ["app/Http/Controllers/AdminController.php", "app/Http/Middleware/RoleMiddleware.php", "app/Livewire/Admin/Dashboard.php", "app/Livewire/Admin/UserManagement.php", "app/Livewire/Admin/PropertyManagement.php", "app/Livewire/Admin/PropertyReports.php", "app/Livewire/Admin/UserReports.php", "app/Livewire/Admin/PropertyTypeManager.php", "app/Livewire/Admin/PropertySubTypeManager.php", "app/Livewire/Admin/AmenityManager.php", "resources/views/livewire/admin/dashboard.blade.php", "resources/views/livewire/admin/user-management.blade.php", "resources/views/livewire/admin/property-management.blade.php", "resources/views/livewire/admin/property-reports.blade.php", "resources/views/livewire/admin/user-reports.blade.php"]}], "project_structure_summary": {"app/Enums": "Contains PHP 8.1+ Backed Enums for type-safe, readable constants (e.g., PropertyStatus).", "app/Models": "Eloquent models representing the database schema, including all relationships.", "config": "Application configuration files, including `permission.php` for role management.", "database/migrations": "Migrations for the structured database schema, including pivot tables and permission tables.", "database/seeders": "Seeders for populating `roles`, `permissions`, `property_types`, `amenities`, etc."}, "cascadingChanges": {"summary": "This section outlines the necessary refactoring across the application to support the new database architecture. The primary goal is to replace JSON-based features and string-based types with relational, type-safe structures, and integrate Spatie/laravel-permission for robust role management.", "backend": [{"file": "app/Livewire/CreateProperty.php", "changes": ["Removed public properties for individual features like `$bedrooms`, `$bathrooms`.", "Added public properties for `$property_type_id`, `$property_sub_type_id`, and an array for `$selected_amenities`.", "Updated the `rules()` method to validate against the new properties and foreign keys.", "Rewrote the `store()` method to create the `Property` and attach its relationships (sub-type, amenities) using Eloquent methods.", "Loaded all `PropertyType` and `Amenity` records in the `mount()` or `render()` method to populate the form's dropdowns and checkboxes."]}, {"file": "app/Http/Controllers/PropertyController.php", "changes": ["Updated the `show()` method to eager-load the new relationships (`propertySubType.propertyType`, `amenities`, `media`) for efficient data retrieval.", "Updated the `store()` and `update()` methods if they are used instead of Livewire components."]}, {"file": "app/Livewire/PropertySearch.php", "changes": ["Rewrote search logic to filter based on the new indexed columns (`bedrooms`, `bathrooms`) and relationships (`whereHas`).", "This resulted in significantly faster and more powerful search queries."]}, {"file": "app/Models/Property.php", "changes": ["Added `propertySubType()` belongsTo relationship.", "Added `amenities()` belongsToMany relationship.", "Added `media()` hasMany relationship.", "Cast the `status` column to the `PropertyStatus` enum."]}, {"file": "app/Models/User.php", "changes": ["Integrated `Spatie\\\\Permission\\\\Traits\\\\HasRoles` trait for role management.", "Removed the `role` column from `$fillable` as roles are now managed by the package."]}, {"file": "database/seeders/PropertySeeder.php", "changes": ["Updated the seeder to create properties according to the new relational structure."]}, {"file": "database/seeders/DatabaseSeeder.php", "changes": ["Updated to call `RolesAndPermissionsSeeder` and `UserRoleSeeder` to properly set up roles and assign them to users."]}, {"file": "database/seeders/RolesAndPermissionsSeeder.php", "changes": ["New seeder created to define roles (admin, agent, seeker) and permissions using Spatie/laravel-permission."]}, {"file": "database/seeders/UserRoleSeeder.php", "changes": ["New seeder created to assign roles to existing and newly created users after roles are defined."]}, {"file": "database/migrations/*_remove_role_from_users_table.php", "changes": ["New migration created to remove the deprecated `role` column from the `users` table."]}, {"file": "app/Http/Middleware/RoleMiddleware.php", "changes": ["Updated to use `\\$request->user()->hasRole(\\$role)` for role-based access control, leveraging Spatie/laravel-permission."]}, {"file": "app/Livewire/Admin/UserManagement.php", "changes": ["Refactored to use Spa<PERSON>/laravel-permission for filtering users by role and assigning roles dynamically."]}, {"file": "routes/web.php", "changes": ["Replaced `role:lister` middleware with `role:agent` to align with the defined user roles.", "Updated route prefix from `/lister` to `/agent` for consistency.", "Added placeholder routes for new admin sections (Analytics, Reports, System)."]}, {"file": "app/Http/Controllers/ProfileController.php", "changes": ["Updated `update` method to handle new agent-specific fields: `agency_name` and `license_number`."]}, {"file": "app/Http/Controllers/AdminController.php", "changes": ["Updated to fetch property type breakdown using the new `PropertyType` model and its relationship with `properties`."]}], "frontend": [{"file": "resources/views/livewire/create-property.blade.php", "changes": ["Replaced the static 'Property Type' dropdown with a dynamic one populated from the `property_types` table.", "Added a second, cascading dropdown for 'Property Sub-Type' that updates based on the selected parent type.", "Replaced the individual feature input fields (`bedrooms`, `bathrooms`) with dedicated inputs that are always visible.", "Added a new section with a list of checkboxes for selecting `amenities`."]}, {"file": "resources/views/properties/show.blade.php", "changes": ["Updated the property details display to render the new structured data, including a list of amenities and the full property type hierarchy, and images from Media Library."]}, {"file": "resources/views/livewire/property-search.blade.php", "changes": ["Updated the search filters to include dynamic dropdowns for property types and sub-types, reflecting the new relational structure.", "Added a cascading dropdown for 'Property Sub-Type' that updates based on the selected parent type."]}, {"file": "resources/views/admin/dashboard.blade.php", "changes": ["Added new UI sections for managing `PropertyTypes` (via `PropertyTypeManager`), `PropertySubTypes` (via `PropertySubTypeManager`), and `Amenities` (via `AmenityManager`) with CRUD interfaces.", "Updated the user management table to display and allow editing of user roles using the new Spatie/laravel-permission integration."]}, {"file": "resources/views/admin/users/index.blade.php", "changes": ["Updated the user table to allow dynamic role assignment via a dropdown, leveraging the Spatie/laravel-permission package."]}, {"file": "resources/views/profile/partials/update-profile-information-form.blade.php", "changes": ["Added agent-specific fields (`agency_name`, `license_number`) that are only visible to users with the 'agent' role."]}, {"file": "resources/views/livewire/admin/property-type-manager.blade.php", "changes": ["New Blade view for the `PropertyTypeManager` Livewire component, providing UI for CRUD operations on property types."]}, {"file": "resources/views/livewire/admin/property-sub-type-manager.blade.php", "changes": ["New Blade view for the `PropertySubTypeManager` Livewire component, providing UI for CRUD operations on property sub-types."]}, {"file": "resources/views/livewire/admin/amenity-manager.blade.php", "changes": ["New Blade view for the `AmenityManager` Livewire component, providing UI for CRUD operations on amenities."]}]}}