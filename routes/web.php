<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ContactController; // Added for Lokus MVP
use App\Http\Controllers\PropertyController; // Added for Lokus MVP
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public Routes
|--------------------------------------------------------------------------
| Routes accessible to all users without authentication
*/

Route::get('/', function () {
    return view('welcome');
})->name('home');

// Public property browsing
Route::get('/properties', [PropertyController::class, 'index'])->name('properties.index');
Route::get('/properties/{property}', [PropertyController::class, 'show'])->name('properties.show');

// Contact functionality
Route::post('/properties/{property}/contact', [ContactController::class, 'send'])->name('properties.contact');

// Contact Form Routes
Route::get('/contact', [ContactController::class, 'showContactForm'])->name('contact.form');
Route::post('/contact', [ContactController::class, 'submitContactForm'])->name('contact.submit');

/*
|--------------------------------------------------------------------------
| Authenticated User Routes
|--------------------------------------------------------------------------
| Routes requiring authentication but no specific role
*/

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Profile Management (consolidated single route pattern)
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'edit'])->name('edit');
        Route::patch('/', [ProfileController::class, 'update'])->name('update');
        Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
    });

    // User Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [ProfileController::class, 'edit'])->name('index');
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile');
        Route::get('/notifications', function () {
            return view('settings.notifications');
        })->name('notifications');
        Route::get('/privacy', function () {
            return view('settings.privacy');
        })->name('privacy');
    });
});

/*
|--------------------------------------------------------------------------
| Agent Routes
|--------------------------------------------------------------------------
| Routes for property agents (formerly listers)
*/

Route::middleware(['auth', 'verified', 'role:agent'])->prefix('agent')->name('agent.')->group(function () {
    // Property Management
    Route::prefix('properties')->name('properties.')->group(function () {
        Route::get('/', [PropertyController::class, 'myProperties'])->name('index');
        Route::get('/create', [PropertyController::class, 'create'])->name('create');
        Route::post('/', [PropertyController::class, 'store'])->name('store');
        Route::get('/{property}/edit', \App\Livewire\EditProperty::class)->name('edit');
        Route::delete('/{property}', [PropertyController::class, 'destroy'])->name('destroy');
        Route::patch('/{property}/status', [PropertyController::class, 'updateStatus'])->name('updateStatus');
    });
});

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
| Routes for administrative functions
*/

Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', \App\Livewire\Admin\Dashboard::class)->name('dashboard');

    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', \App\Livewire\Admin\UserManagement::class)->name('index');
    });

    // Property Management
    Route::prefix('properties')->name('properties.')->group(function () {
        Route::get('/', \App\Livewire\Admin\PropertyManagement::class)->name('index');
        Route::get('/{property}/edit', [\App\Http\Controllers\AdminController::class, 'editProperty'])->name('edit');
        Route::put('/{property}', [\App\Http\Controllers\AdminController::class, 'updateProperty'])->name('update');
        Route::patch('/{property}/status', [\App\Http\Controllers\AdminController::class, 'updatePropertyStatus'])->name('updateStatus');
        Route::patch('/{property}/toggle-featured', [\App\Http\Controllers\AdminController::class, 'toggleFeatured'])->name('toggleFeatured');
        Route::delete('/{property}', [\App\Http\Controllers\AdminController::class, 'destroyProperty'])->name('destroy');
    });
    
    // Favorites Management
    Route::get('/favorites', \App\Livewire\Admin\FavoriteManager::class)->name('favorites');

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/users', \App\Livewire\Admin\UserReports::class)->name('users');
        Route::get('/properties', \App\Livewire\Admin\PropertyReports::class)->name('properties');
    });

    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', \App\Livewire\Admin\Settings::class)->name('index');
        Route::get('/security', function () { return view('admin.settings.security'); })->name('security');
        Route::get('/health', function () { return view('admin.settings.health'); })->name('health');
    });
});

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
| Include Laravel's authentication routes (login, register, etc.)
*/

require __DIR__.'/auth.php';
