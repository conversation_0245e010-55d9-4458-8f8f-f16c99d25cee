{"appName": "<PERSON><PERSON>", "description": "A web application for listing, searching, and managing real estate properties. It features robust user role management via Spatie/laravel-permission, and includes functionalities like property creation, advanced search with structured data, user profile management, and a comprehensive admin dashboard for site-wide management.", "technologies": {"backend": "PHP / Laravel", "frontend": "Blade, Livewire, Tailwind CSS, JavaScript, Vite", "database": "SQL (using SQLite for development)", "packages": ["spatie/laravel-permission", "spatie/laravel-medialibrary"], "testing": "PHPUnit, Pest"}, "features": [{"name": "User Authentication & Role Management", "description": "Handles user registration, login, password reset, and email verification. Manages user sessions and controls access to features based on roles defined by Spatie/laravel-permission.", "keyFiles": ["routes/auth.php", "app/Http/Controllers/Auth/", "app/Http/Requests/Auth/", "resources/views/auth/", "app/Models/User.php", "database/seeders/RolesAndPermissionsSeeder.php", "database/seeders/UserRoleSeeder.php", "app/Http/Middleware/RoleMiddleware.php"]}, {"name": "Property Management", "description": "Core functionality for creating, viewing, updating, and deleting properties. Users can manage their own listings, which now utilize a structured data model with dedicated tables for property types, sub-types, and amenities.", "keyFiles": ["app/Http/Controllers/PropertyController.php", "app/Models/Property.php", "app/Models/PropertyType.php", "app/Models/PropertySubType.php", "app/Models/Amenity.php", "app/Livewire/CreateProperty.php", "app/Livewire/MyProperties.php", "database/migrations/2025_05_27_054643_create_properties_table.php", "database/migrations/*_create_property_types_table.php", "database/migrations/*_create_property_sub_types_table.php", "database/migrations/*_create_amenities_table.php", "database/migrations/*_create_amenity_property_table.php", "resources/views/properties/"]}, {"name": "Property Search and Discovery", "description": "A public-facing feature that allows any visitor to search and filter through the property listings using the new structured data (property types, sub-types, bedrooms, bathrooms, amenities).", "keyFiles": ["app/Livewire/PropertySearch.php", "resources/views/welcome.blade.php", "resources/views/livewire/property-search.blade.php"]}, {"name": "User Profile", "description": "Allows users to view and update their profile information, including name, email, and phone number.", "keyFiles": ["app/Http/Controllers/ProfileController.php", "app/Http/Requests/ProfileUpdateRequest.php", "resources/views/profile/"]}, {"name": "Favorites System", "description": "Enables authenticated users to mark properties as 'favorites' and view their list of saved properties.", "keyFiles": ["app/Livewire/FavoriteButton.php", "app/Livewire/FavoriteProperties.php", "database/migrations/2025_06_18_180852_create_favorites_table.php"]}, {"name": "Admin Dashboard", "description": "A dedicated backend interface for administrators to manage the application's data, including users (with role assignment), properties, property types, sub-types, and amenities. Now migrated to Livewire components for enhanced interactivity. Access is restricted by roles defined by Spatie/laravel-permission.", "keyFiles": ["app/Http/Controllers/AdminController.php", "app/Http/Middleware/RoleMiddleware.php", "app/Livewire/Admin/Dashboard.php", "app/Livewire/Admin/UserManagement.php", "app/Livewire/Admin/PropertyManagement.php", "app/Livewire/Admin/PropertyReports.php", "app/Livewire/Admin/UserReports.php", "app/Livewire/Admin/PropertyTypeManager.php", "app/Livewire/Admin/PropertySubTypeManager.php", "app/Livewire/Admin/AmenityManager.php", "resources/views/livewire/admin/dashboard.blade.php", "resources/views/livewire/admin/user-management.blade.php", "resources/views/livewire/admin/property-management.blade.php", "resources/views/livewire/admin/property-reports.blade.php", "resources/views/livewire/admin/user-reports.blade.php"]}, {"name": "Property Inquiry", "description": "Allows users to send an email inquiry to the property contact.", "keyFiles": ["app/Mail/PropertyInquiry.php", "app/Http/Controllers/ContactController.php", "resources/views/emails/property-inquiry.blade.php"]}, {"name": "Command-Line Interface (CLI)", "description": "Custom commands for application maintenance and testing, such as seeding the database with test users, roles, permissions, and properties.", "keyFiles": ["artisan", "app/Console/Commands/TestPropertyCreation.php", "database/seeders/"]}], "project_structure_summary": {"app": "Contains the core application logic: Models, Controllers, Livewire Components, and Providers.", "config": "Application configuration files, including `permission.php` for role management.", "database": "Holds database migrations, factories, and seeders.", "public": "Web server's document root, containing the entry point `index.php` and compiled assets.", "resources": "Contains frontend assets like CSS, JavaScript, and Blade view templates.", "routes": "API and web route definitions (`web.php`, `auth.php`).", "storage": "Framework-generated files, caches, logs, and user-uploaded files.", "tests": "Contains all application tests, separated into Feature and Unit tests."}}