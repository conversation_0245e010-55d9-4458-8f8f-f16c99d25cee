{"appName": "Lokus UI", "description": "Describes the user interface structure, components, and design system for the Lokus application.", "designSystem": {"framework": "Tailwind CSS", "branding": {"logo": "resources/views/components/app-logo.blade.php", "icon": "resources/views/components/app-logo-icon.blade.php", "favicon": "public/favicon.ico"}, "cssEntryPoints": {"mainApp": "resources/css/app.css", "adminDashboard": "resources/css/admin-dashboard.css"}, "assetBundling": "Vite (vite.config.js)"}, "layouts": [{"name": "Guest Layout", "description": "The main layout for unauthenticated users. Used for pages like login, registration, and the public-facing welcome page.", "keyFile": "app/View/Components/GuestLayout.php"}, {"name": "Admin Dashboard", "description": "A separate dashboard for administrators to manage site content, including property taxonomies and user roles, now migrated to Livewire components for enhanced interactivity.", "viewFile": "resources/views/livewire/admin/dashboard.blade.php"}], "pages": [{"name": "Welcome Page", "description": "The public landing page for all visitors. Features the primary property search functionality.", "viewFile": "resources/views/welcome.blade.php", "mainComponent": "app/Livewire/PropertySearch.php"}, {"name": "User Dashboard", "description": "The main dashboard for logged-in users.", "viewFile": "resources/views/dashboard.blade.php"}, {"name": "Admin Dashboard", "description": "A separate dashboard for administrators to manage site content, including property taxonomies and user roles.", "viewFile": "resources/views/admin/dashboard.blade.php"}, {"name": "Login/Register Pages", "description": "A collection of views for user authentication flows.", "viewFiles": "resources/views/auth/"}, {"name": "Profile Page", "description": "Page for users to edit their profile information.", "viewFiles": "resources/views/profile/"}], "interactiveComponents": {"description": "Core UI features are powered by Livewire components, enabling dynamic, real-time user interactions without full page reloads.", "components": [{"name": "PropertySearch", "description": "Handles real-time searching and filtering of property listings using structured data.", "keyFile": "app/Livewire/PropertySearch.php"}, {"name": "CreateProperty", "description": "A form component for users to create and submit new property listings, now supporting structured property types, sub-types, and amenities.", "keyFile": "app/Livewire/CreateProperty.php"}, {"name": "MyProperties", "description": "Displays a list of properties owned by the currently logged-in user.", "keyFile": "app/Livewire/MyProperties.php"}, {"name": "FavoriteButton", "description": "A toggle button to add or remove a property from the user's favorites.", "keyFile": "app/Livewire/FavoriteButton.php"}, {"name": "FavoriteProperties", "description": "Displays the list of properties a user has marked as favorite.", "keyFile": "app/Livewire/FavoriteProperties.php"}, {"name": "Dashboard (Admin)", "description": "The main admin dashboard component, providing an overview and navigation to other admin sections.", "keyFile": "app/Livewire/Admin/Dashboard.php"}, {"name": "UserManagement (Admin)", "description": "An admin-only component for viewing, activating, deactivating, and assigning roles to user accounts using Spatie/laravel-permission.", "keyFile": "app/Livewire/Admin/UserManagement.php"}, {"name": "PropertyManagement (Admin)", "description": "An admin-only component for managing property listings (CRUD).", "keyFile": "app/Livewire/Admin/PropertyManagement.php"}, {"name": "PropertyReports (Admin)", "description": "An admin-only component for viewing detailed reports and statistics on properties.", "keyFile": "app/Livewire/Admin/PropertyReports.php"}, {"name": "UserReports (Admin)", "description": "An admin-only component for viewing detailed reports and statistics on users.", "keyFile": "app/Livewire/Admin/UserReports.php"}, {"name": "PropertyTypeManager (Admin)", "description": "An admin-only component for managing property types (CRUD).", "keyFile": "app/Livewire/Admin/PropertyTypeManager.php"}, {"name": "PropertySubTypeManager (Admin)", "description": "An admin-only component for managing property sub-types (CRUD), linked to property types.", "keyFile": "app/Livewire/Admin/PropertySubTypeManager.php"}, {"name": "Amenity<PERSON>anager (Admin)", "description": "An admin-only component for managing amenities (CRUD).", "keyFile": "app/Livewire/Admin/AmenityManager.php"}]}, "reusableUIElements": {"description": "A collection of smaller, reusable Blade components used to build the application's UI consistently.", "keyFiles": "resources/views/components/", "examples": ["nav-link.blade.php", "dropdown.blade.php", "modal.blade.php", "input-label.blade.php"]}}