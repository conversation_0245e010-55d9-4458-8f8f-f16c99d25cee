<?php

namespace Database\Factories;

use App\Models\PropertySubType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Property>
 */
class PropertyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::inRandomOrder()->first()->id,
            'property_sub_type_id' => PropertySubType::inRandomOrder()->first()->id,
            'listing_type' => $this->faker->randomElement(['for_sale', 'for_rent']),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(3),
            'price' => $this->faker->numberBetween(50000, 2000000),
            'currency' => 'USD',
            'address_line_1' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'state_region' => $this->faker->stateAbbr,
            'zip_code' => $this->faker->postcode,
            'bedrooms' => $this->faker->numberBetween(1, 6),
            'bathrooms' => $this->faker->numberBetween(1, 4),
            'square_footage' => $this->faker->numberBetween(500, 5000),
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'status' => $this->faker->randomElement(['published', 'draft', 'sold', 'rented']),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
        ];
    }
}
