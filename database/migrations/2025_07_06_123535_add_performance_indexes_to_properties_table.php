<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            // Composite indexes for frequently queried combinations
            $table->index(['user_id', 'status'], 'properties_user_status_index');
            $table->index(['city', 'listing_type'], 'properties_city_listing_type_index');
            $table->index(['property_sub_type_id', 'status'], 'properties_subtype_status_index');

            // Individual indexes for commonly filtered columns
            $table->index('status', 'properties_status_index');
            $table->index('city', 'properties_city_index');
            $table->index('listing_type', 'properties_listing_type_index');
            $table->index('created_at', 'properties_created_at_index');

            // Index for price range queries
            $table->index('price', 'properties_price_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            // Drop composite indexes
            $table->dropIndex('properties_user_status_index');
            $table->dropIndex('properties_city_listing_type_index');
            $table->dropIndex('properties_subtype_status_index');

            // Drop individual indexes
            $table->dropIndex('properties_status_index');
            $table->dropIndex('properties_city_index');
            $table->dropIndex('properties_listing_type_index');
            $table->dropIndex('properties_created_at_index');
            $table->dropIndex('properties_price_index');
        });
    }
};
