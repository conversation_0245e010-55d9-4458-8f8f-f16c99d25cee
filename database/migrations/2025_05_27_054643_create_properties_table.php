<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('property_sub_type_id')->constrained('property_sub_types')->onDelete('cascade');
            $table->string('listing_type'); // For Sale, For Rent
            $table->string('title');
            $table->text('description');
            $table->decimal('price', 15, 2);
            $table->string('currency')->default('USD');
            $table->string('address_line_1');
            $table->string('city');
            $table->string('state_region')->nullable();
            $table->string('zip_code')->nullable();
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            $table->unsignedInteger('square_footage')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('status')->default('draft'); // draft, published, sold, rented, under_offer
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties');
    }
};
