<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Assign roles to existing users
        $admin = User::where('email', '<EMAIL>')->first();
        if ($admin) {
            $admin->assignRole('admin');
        }

        $lister1 = User::where('email', '<EMAIL>')->first();
        if ($lister1) {
            $lister1->assignRole('agent');
        }

        $lister2 = User::where('email', '<EMAIL>')->first();
        if ($lister2) {
            $lister2->assignRole('agent');
        }

        $seeker = User::where('email', '<EMAIL>')->first();
        if ($seeker) {
            $seeker->assignRole('seeker');
        }

        // Assign roles to additional factory-created users
        User::whereNotIn('email', ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'])
            ->each(function ($user) {
                // Assign 'agent' role to some, 'seeker' to others
                if (rand(0, 1)) {
                    $user->assignRole('agent');
                } else {
                    $user->assignRole('seeker');
                }
            });
    }
}
