<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create test users with known credentials
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '******-0101',
            'is_active' => true,
        ]);

        User::factory()->create([
            'name' => 'John Lister',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '******-0102',
            'is_active' => true,
        ]);

        User::factory()->create([
            'name' => 'Sarah Property Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '******-0103',
            'is_active' => true,
        ]);

        User::factory()->create([
            'name' => '<PERSON> Seeker',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '******-0104',
            'is_active' => true,
        ]);

        // Create additional users
        User::factory(5)->create();
        User::factory(8)->create();

        $this->call([
            RolesAndPermissionsSeeder::class,
            PropertyTypeSeeder::class,
            PropertySubTypeSeeder::class,
            AmenitySeeder::class,
            UserRoleSeeder::class, // Call the new seeder here
        ]);

        // Call property seeder
        $this->call(PropertySeeder::class);
    }
}
