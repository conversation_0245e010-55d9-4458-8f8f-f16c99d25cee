<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PropertyType;
use App\Models\PropertySubType;

class PropertySubTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subTypes = [
            'Residential' => ['Bedsitter', 'Single Room', 'Double Room', 'Apartment', 'Bungalow', 'Townhouse', 'Standalone'],
            'Commercial' => ['Hotel', 'Hostel', 'Office', 'Shop'],
            'Industrial' => ['Godown'],
            'Land' => ['Land'],
        ];

        foreach ($subTypes as $typeName => $subTypeNames) {
            $type = PropertyType::where('name', $typeName)->first();
            if ($type) {
                foreach ($subTypeNames as $subTypeName) {
                    PropertySubType::create([
                        'property_type_id' => $type->id,
                        'name' => $subTypeName,
                    ]);
                }
            }
        }
    }
}
