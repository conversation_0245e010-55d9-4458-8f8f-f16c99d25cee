<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Amenity;

class AmenitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $amenities = [
            'Swimming Pool', 'Gym', 'Parking', 'Security', 'Balcony', 'Garden',
            'Air Conditioning', 'Heating', 'Wi-Fi', 'Pet Friendly', 'Furnished',
            'Unfurnished', 'Washer/Dryer', 'Dishwasher', 'Fireplace', 'Elevator'
        ];

        foreach ($amenities as $amenity) {
            Amenity::create(['name' => $amenity]);
        }
    }
}
