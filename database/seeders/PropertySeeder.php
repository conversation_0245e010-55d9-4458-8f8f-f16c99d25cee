<?php

namespace Database\Seeders;

use App\Models\Amenity;
use App\Models\Property;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all amenities
        $amenities = Amenity::all();
        if ($amenities->isEmpty()) {
            $this->command->warn('No amenities found. Please run the AmenitySeeder first.');
            return;
        }

        // Get all available property images
        $imagePath = storage_path('app/public/properties');
        if (!File::exists($imagePath)) {
            $this->command->warn('Property images directory not found at storage/app/public/properties.');
            return;
        }
        $images = File::files($imagePath);
        if (empty($images)) {
            $this->command->warn('No images found in storage/app/public/properties.');
            return;
        }

        // Create 50 properties using the factory
        Property::factory(50)->create()->each(function ($property) use ($amenities, $images) {
            // Attach a random number of amenities (1 to 5)
            $randomAmenities = $amenities->random(rand(1, 5));
            $property->amenities()->attach($randomAmenities);

            // Attach a random number of images (1 to 5)
            $randomImages = collect($images)->random(rand(1, min(5, count($images))));
            foreach ($randomImages as $image) {
                $property->addMedia($image->getPathname())
                         ->preservingOriginal()
                         ->toMediaCollection('gallery');
            }
        });

        $this->command->info('Created 50 properties, attached amenities, and added images.');
    }
}
