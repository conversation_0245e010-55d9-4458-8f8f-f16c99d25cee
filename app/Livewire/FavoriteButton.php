<?php

namespace App\Livewire;

use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class FavoriteButton extends Component
{
    public $property;
    public $isFavorited;

    public function mount(Property $property)
    {
        $this->property = $property;
        $this->checkIfFavorited();
    }

    public function checkIfFavorited()
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            $this->isFavorited = $user->favorites()->where('property_id', $this->property->id)->exists();
        } else {
            $this->isFavorited = false;
        }
    }

    public function toggleFavorite()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        /** @var \App\Models\User $user */
        $user = Auth::user();
        
        if ($this->isFavorited) {
            $user->favorites()->detach($this->property->id);
        } else {
            $user->favorites()->attach($this->property->id);
        }

        $this->checkIfFavorited();
    }

    public function render()
    {
        return view('livewire.favorite-button');
    }
}
