<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Models\Property;
use App\Models\PropertyType;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.admin', ['title' => 'Admin Dashboard'])]
class Dashboard extends Component
{
    public function render()
    {
        // Basic counts
        $totalUsers = User::count();
        $totalProperties = Property::count();
        $publishedProperties = Property::where('status', 'published')->count();
        $pendingProperties = Property::where('status', 'draft')->count();

        // User statistics by role
        $adminUsers = User::role('admin')->count();
        $agentUsers = User::role('agent')->count();
        $seekerUsers = User::role('seeker')->count();
        $activeUsers = User::where('is_active', true)->count();
        $inactiveUsers = User::where('is_active', false)->count();

        // Property statistics by status
        $soldProperties = Property::where('status', 'sold')->count();
        $rentedProperties = Property::where('status', 'rented')->count();
        $underOfferProperties = Property::where('status', 'under_offer')->count();

        // Property types breakdown
        $propertyTypes = PropertyType::withCount('properties')
            ->get()
            ->pluck('properties_count', 'name')
            ->toArray();
        $propertyTypePercentages = [];
        foreach ($propertyTypes as $type => $count) {
            $propertyTypePercentages[$type] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Listing types breakdown
        $listingTypes = Property::selectRaw('listing_type, COUNT(*) as count')
            ->groupBy('listing_type')
            ->pluck('count', 'listing_type')
            ->toArray();
        $listingTypePercentages = [];
        foreach ($listingTypes as $type => $count) {
            $listingTypePercentages[$type] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Recent activity - last 5 properties and users
        $recentProperties = Property::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Average property price
        $averagePrice = Property::where('status', 'published')->avg('price');

        // Properties by city (top 5)
        $topCities = Property::selectRaw('city, COUNT(*) as count')
            ->groupBy('city')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->pluck('count', 'city')
            ->toArray();
        $topCityPercentages = [];
        foreach ($topCities as $city => $count) {
            $topCityPercentages[$city] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        return view('livewire.admin.dashboard', compact(
            'totalUsers', 'totalProperties', 'publishedProperties', 'pendingProperties',
            'adminUsers', 'agentUsers', 'seekerUsers', 'activeUsers', 'inactiveUsers',
            'soldProperties', 'rentedProperties', 'underOfferProperties',
            'propertyTypes', 'propertyTypePercentages', 'listingTypes', 'listingTypePercentages', 
            'topCities', 'topCityPercentages', 'averagePrice',
            'recentProperties', 'recentUsers'
        ));
    }
}
