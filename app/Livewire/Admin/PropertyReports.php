<?php

namespace App\Livewire\Admin;

use App\Models\Property;
use App\Models\PropertyType;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.admin', ['title' => 'Property Reports'])]
class PropertyReports extends Component
{
    use WithPagination;
    public $search = '';
    public $statusFilter = '';
    public $typeFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'typeFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updating($key)
    {
        if (in_array($key, ['search', 'statusFilter', 'typeFilter', 'sortField', 'sortDirection'])) {
            $this->resetPage();
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $properties = Property::query();

        if ($this->search) {
            $properties->where(function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhere('address', 'like', '%' . $this->search . '%')
                      ->orWhere('city', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->statusFilter) {
            $properties->where('status', $this->statusFilter);
        }

        if ($this->typeFilter) {
            $properties->where('property_type_id', $this->typeFilter);
        }

        $properties->orderBy($this->sortField, $this->sortDirection);

        // Statistics
        $totalProperties = Property::count();
        $publishedProperties = Property::where('status', 'published')->count();
        $draftProperties = Property::where('status', 'draft')->count();
        $soldProperties = Property::where('status', 'sold')->count();
        $rentedProperties = Property::where('status', 'rented')->count();
        $underOfferProperties = Property::where('status', 'under_offer')->count();
        $averagePrice = Property::where('status', 'published')->avg('price');
        $propertyTypes = PropertyType::all();

        return view('livewire.admin.property-reports', [
            'properties' => $properties->paginate(10),
            'totalProperties' => $totalProperties,
            'publishedProperties' => $publishedProperties,
            'draftProperties' => $draftProperties,
            'soldProperties' => $soldProperties,
            'rentedProperties' => $rentedProperties,
            'underOfferProperties' => $underOfferProperties,
            'averagePrice' => $averagePrice,
            'propertyTypes' => $propertyTypes,
        ]);
    }
}
