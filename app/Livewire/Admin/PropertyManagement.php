<?php

namespace App\Livewire\Admin;

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.admin', ['title' => 'Property Management'])]
class PropertyManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $propertyTypeFilter = '';
    public $propertySubTypeFilter = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    protected $queryString = [
        'search' => ['except' => ''],
        'propertyTypeFilter' => ['except' => ''],
        'propertySubTypeFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updating($key)
    {
        if (in_array($key, ['search', 'propertyTypeFilter', 'propertySubTypeFilter', 'statusFilter', 'sortField', 'sortDirection'])) {
            $this->resetPage();
        }
    }

    public function updatedPropertyTypeFilter()
    {
        $this->propertySubTypeFilter = '';
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $properties = Property::query()->with('user');

        if ($this->search) {
            $properties->where(function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhere('city', 'like', '%' . $this->search . '%')
                      ->orWhere('state_region', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->propertySubTypeFilter) {
            $properties->where('property_sub_type_id', $this->propertySubTypeFilter);
        } elseif ($this->propertyTypeFilter) {
            $properties->whereHas('propertySubType', function ($query) {
                $query->where('property_type_id', $this->propertyTypeFilter);
            });
        }

        if ($this->statusFilter !== '') {
            $properties->where('status', $this->statusFilter);
        }

        $properties->orderBy($this->sortField, $this->sortDirection);

        $propertyTypes = PropertyType::all();
        $propertySubTypes = $this->propertyTypeFilter ? PropertySubType::where('property_type_id', $this->propertyTypeFilter)->get() : collect();

        return view('livewire.admin.property-management', [
            'properties' => $properties->paginate(10),
            'propertyTypes' => $propertyTypes,
            'propertySubTypes' => $propertySubTypes,
        ]);
    }

    public function toggleFeatured($propertyId)
    {
        $property = Property::findOrFail($propertyId);
        $property->is_featured = !$property->is_featured;
        $property->save();
    }

    public function updateStatus($propertyId, $status)
    {
        $property = Property::findOrFail($propertyId);
        $property->status = $status;
        $property->save();
    }

    public function deleteProperty($propertyId)
    {
        Property::destroy($propertyId);
    }
}
