<?php

namespace App\Livewire\Admin;

use App\Models\PropertySubType;
use App\Models\PropertyType;
use Livewire\Component;
use Livewire\WithPagination;

class PropertySubTypeManager extends Component
{
    use WithPagination;

    public $name = '';
    public $property_type_id = '';
    public $property_sub_type_id;
    public $is_editing = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'property_type_id' => 'required|exists:property_types,id',
    ];

    public function render()
    {
        return view('livewire.admin.property-sub-type-manager', [
            'propertySubTypes' => PropertySubType::with('propertyType')->paginate(10),
            'propertyTypes' => PropertyType::all(),
        ]);
    }

    public function savePropertySubType()
    {
        $this->validate();

        PropertySubType::create([
            'name' => $this->name,
            'property_type_id' => $this->property_type_id,
        ]);

        $this->reset(['name', 'property_type_id']);
        session()->flash('message', 'Property sub-type created successfully.');
    }

    public function edit($id)
    {
        $propertySubType = PropertySubType::findOrFail($id);
        $this->property_sub_type_id = $id;
        $this->name = $propertySubType->name;
        $this->property_type_id = $propertySubType->property_type_id;
        $this->is_editing = true;
    }

    public function updatePropertySubType()
    {
        $this->validate();

        $propertySubType = PropertySubType::findOrFail($this->property_sub_type_id);
        $propertySubType->update([
            'name' => $this->name,
            'property_type_id' => $this->property_type_id,
        ]);

        $this->reset(['name', 'property_type_id', 'property_sub_type_id', 'is_editing']);
        session()->flash('message', 'Property sub-type updated successfully.');
    }

    public function delete($id)
    {
        PropertySubType::findOrFail($id)->delete();
        session()->flash('message', 'Property sub-type deleted successfully.');
    }
}
