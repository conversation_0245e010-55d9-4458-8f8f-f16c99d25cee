<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.app', ['title' => 'Admin Settings'])]
class Settings extends Component
{
    public $activeTab = 'taxonomy';

    public function render()
    {
        return view('livewire.admin.settings');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }
}
