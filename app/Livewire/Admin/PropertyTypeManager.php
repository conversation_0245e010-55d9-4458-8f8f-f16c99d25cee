<?php

namespace App\Livewire\Admin;

use App\Models\PropertyType;
use Livewire\Component;
use Livewire\WithPagination;

class PropertyTypeManager extends Component
{
    use WithPagination;

    public $name = '';
    public $property_type_id;
    public $is_editing = false;

    protected $rules = [
        'name' => 'required|string|max:255|unique:property_types,name',
    ];

    public function render()
    {
        return view('livewire.admin.property-type-manager', [
            'propertyTypes' => PropertyType::paginate(10),
        ]);
    }

    public function savePropertyType()
    {
        $this->validate();

        PropertyType::create(['name' => $this->name]);

        $this->reset(['name']);
        session()->flash('message', 'Property type created successfully.');
    }

    public function edit($id)
    {
        $propertyType = PropertyType::findOrFail($id);
        $this->property_type_id = $id;
        $this->name = $propertyType->name;
        $this->is_editing = true;
    }

    public function updatePropertyType()
    {
        $this->validate([
            'name' => 'required|string|max:255|unique:property_types,name,' . $this->property_type_id,
        ]);

        $propertyType = PropertyType::findOrFail($this->property_type_id);
        $propertyType->update(['name' => $this->name]);

        $this->reset(['name', 'property_type_id', 'is_editing']);
        session()->flash('message', 'Property type updated successfully.');
    }

    public function delete($id)
    {
        PropertyType::findOrFail($id)->delete();
        session()->flash('message', 'Property type deleted successfully.');
    }
}
