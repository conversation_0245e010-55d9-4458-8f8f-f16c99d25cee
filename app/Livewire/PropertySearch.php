<?php

namespace App\Livewire;

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySubType;
use Livewire\Component;
use Livewire\WithPagination;

class PropertySearch extends Component
{
    use WithPagination;

    public $keywords = '';
    public $property_type_id = '';
    public $property_sub_type_id = '';
    public $listing_type = '';
    public $min_price = '';
    public $max_price = '';
    public $sort_by = 'created_at';
    public $sort_direction = 'desc';
    public $total_properties;

    protected $queryString = [
        'keywords' => ['except' => ''],
        'property_type_id' => ['except' => ''],
        'property_sub_type_id' => ['except' => ''],
        'listing_type' => ['except' => ''],
        'min_price' => ['except' => ''],
        'max_price' => ['except' => ''],
        'sort_by' => ['except' => 'created_at'],
        'sort_direction' => ['except' => 'desc'],
    ];


    public function updating($key)
    {
        if (in_array($key, ['keywords', 'property_type_id', 'property_sub_type_id', 'listing_type', 'min_price', 'max_price', 'sort_by', 'sort_direction'])) {
            $this->resetPage();
        }
    }

    public function updatedPropertyTypeId()
    {
        $this->property_sub_type_id = '';
        $this->resetPage();
    }

    public function render()
    {
        // Calculate total properties before any filtering
        $this->total_properties = Property::where('status', 'published')->count();

        $query = Property::with(['user', 'propertySubType.propertyType']);

        // Apply keywords search
        if ($this->keywords) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->keywords . '%')
                    ->orWhere('description', 'like', '%' . $this->keywords . '%')
                    ->orWhere('city', 'like', '%' . $this->keywords . '%')
                    ->orWhere('state_region', 'like', '%' . $this->keywords . '%');
            });
        }

        // Apply filters
        if ($this->property_sub_type_id) {
            $query->where('property_sub_type_id', $this->property_sub_type_id);
        } elseif ($this->property_type_id) {
            $query->whereHas('propertySubType', function ($q) {
                $q->where('property_type_id', $this->property_type_id);
            });
        }

        if ($this->listing_type) {
            $query->where('listing_type', $this->listing_type);
        }

        if ($this->min_price) {
            $query->where('price', '>=', $this->min_price);
        }

        if ($this->max_price) {
            $query->where('price', '<=', $this->max_price);
        }

        // Only show published properties
        $query->where('status', 'published');

        // Apply sorting
        $query->orderBy($this->sort_by, $this->sort_direction);

        $properties = $query->paginate(10);

        // Convert to array for JavaScript
        $propertiesData = $properties->getCollection()->map(function ($property) {
            return $property->only([
                'id',
                'title',
                'latitude',
                'longitude'
            ]);
        })->all();

        // Only dispatch event if properties have changed
        if ($this->propertiesData != $propertiesData) {
            $this->propertiesData = $propertiesData;
            $this->dispatch('propertiesUpdated', properties: $propertiesData);
        }

        $propertyTypes = PropertyType::all();
        $propertySubTypes = $this->property_type_id ? PropertySubType::where('property_type_id', $this->property_type_id)->get() : collect();

        return view('livewire.property-search', [
            'properties' => $properties,
            'propertiesData' => $propertiesData,
            'propertyTypes' => $propertyTypes,
            'propertySubTypes' => $propertySubTypes,
        ]);
    }

    // Add this property to the class
    protected $propertiesData = [];


    public function showProperty($propertyId)
    {
        return redirect()->route('properties.show', $propertyId);
    }

    public function search()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->reset([
            'keywords',
            'property_type_id',
            'property_sub_type_id',
            'listing_type',
            'min_price',
            'max_price',
            'sort_by',
            'sort_direction'
        ]);
        $this->resetPage();
    }

    // Add this to trigger events when properties update
    protected $listeners = ['filtersUpdated' => 'updateProperties'];

    public function updateProperties()
    {
        $this->emit('propertiesUpdated');
    }
}
