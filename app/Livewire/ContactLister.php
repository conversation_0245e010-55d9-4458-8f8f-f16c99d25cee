<?php

namespace App\Livewire;

use App\Mail\PropertyInquiry;
use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Livewire\Attributes\On; // Import the On attribute

class ContactLister extends Component
{
    public ?Property $property = null; // Will be an instance of Property
    public $name = '';
    public $email = '';
    public $phone = '';
    public $message = '';
    public $showModal = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'message' => 'required|string|min:10',
    ];

    public function mount()
    {
        if (Auth::check()) {
            $this->name = Auth::user()->name;
            $this->email = Auth::user()->email;
            $this->phone = Auth::user()->phone;
        }
    }

    #[On('open-contact-modal')]
    public function openModal($propertyId)
    {
        $this->property = Property::with('user')->findOrFail($propertyId);
        $this->showModal = true;
    }

    public function sendInquiry()
    {
        $this->validate();

        Mail::to($this->property->user->email)->send(new PropertyInquiry(
            $this->property,
            $this->name,
            $this->email,
            $this->phone,
            $this->message
        ));

        session()->flash('inquiry_message', 'Your inquiry has been sent successfully!');
        $this->reset(['name', 'email', 'phone', 'message']); // Clear form fields
        $this->showModal = false; // Close the modal
    }

    public function render()
    {
        return view('livewire.contact-lister');
    }
}
