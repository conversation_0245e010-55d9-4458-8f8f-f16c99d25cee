<?php

namespace App\Livewire;

use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;

class MyProperties extends Component
{
    use WithPagination;

    protected $listeners = ['propertyDeleted' => '$refresh'];

    public function deleteProperty(Property $property)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        // Delete associated images from storage
        if ($property->images) {
            foreach ($property->images as $imagePath) {
                // Remove '/storage/' prefix to get the path relative to 'public' disk
                $diskPath = str_replace('/storage/', 'public/', $imagePath);
                if (Storage::exists($diskPath)) {
                    Storage::delete($diskPath);
                }
            }
        }

        $property->delete();
        session()->flash('message', 'Property deleted successfully!');
    }

    public function updatePropertyStatus(Property $property, $status)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        $property->status = $status;
        $property->save();
        session()->flash('message', 'Property status updated successfully!');
    }

    public function render()
    {
        $properties = \App\Models\User::find(Auth::id())
            ->properties()
            ->with(['propertySubType.propertyType', 'media'])
            ->paginate(10);
        return view('livewire.my-properties', [
            'properties' => $properties,
        ]);
    }
}
