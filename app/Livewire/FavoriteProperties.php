<?php

namespace App\Livewire;

use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class FavoriteProperties extends Component
{
    public $properties;

    public function mount()
    {
        $this->loadProperties();
    }

    public function loadProperties()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $this->properties = $user->favorites()
            ->with(['propertySubType.propertyType', 'media', 'user'])
            ->get();
    }

    public function render()
    {
        return view('livewire.favorite-properties');
    }
}
