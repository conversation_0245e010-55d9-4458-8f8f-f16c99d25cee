<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PropertyType extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    public function propertySubTypes(): HasMany
    {
        return $this->hasMany(PropertySubType::class);
    }

    public function properties(): HasMany
    {
        return $this->hasMany(Property::class, 'property_sub_type_id', 'id');
    }
}
