<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Property;
use App\Models\PropertyType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage; // Needed for deleting images
use Intervention\Image\ImageManager; // Import the ImageManager
use Intervention\Image\Drivers\Gd\Driver; // Import the GD driver

class AdminController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        // Basic counts
        $totalUsers = User::count();
        $totalProperties = Property::count();
        $publishedProperties = Property::where('status', 'published')->count();
        $pendingProperties = Property::where('status', 'draft')->count();

        // User statistics by role
        $adminUsers = User::role('admin')->count();
        $agentUsers = User::role('agent')->count();
        $seekerUsers = User::role('seeker')->count();
        $activeUsers = User::where('is_active', true)->count();
        $inactiveUsers = User::where('is_active', false)->count();

        // Property statistics by type and status
        $soldProperties = Property::where('status', 'sold')->count();
        $rentedProperties = Property::where('status', 'rented')->count();
        $underOfferProperties = Property::where('status', 'under_offer')->count();

        // Property types breakdown
        $propertyTypes = PropertyType::withCount('properties')
            ->get()
            ->pluck('properties_count', 'name')
            ->toArray();

        // Listing types breakdown
        $listingTypes = Property::selectRaw('listing_type, COUNT(*) as count')
            ->groupBy('listing_type')
            ->pluck('count', 'listing_type')
            ->toArray();

        // Recent activity - last 10 properties and users
        $recentProperties = Property::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Average property price
        $averagePrice = Property::where('status', 'published')->avg('price');

        // Properties by city (top 5)
        $topCities = Property::selectRaw('city, COUNT(*) as count')
            ->groupBy('city')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->pluck('count', 'city')
            ->toArray();

        return view('admin.dashboard', compact(
            'totalUsers', 'totalProperties', 'publishedProperties', 'pendingProperties',
            'adminUsers', 'agentUsers', 'seekerUsers', 'activeUsers', 'inactiveUsers',
            'soldProperties', 'rentedProperties', 'underOfferProperties',
            'propertyTypes', 'listingTypes', 'topCities', 'averagePrice',
            'recentProperties', 'recentUsers'
        ));
    }


    /**
     * Display a listing of all properties (for admin).
     */
    public function viewProperties(Request $request)
    {
        $query = Property::query();

        // Apply filters
        $filters = [
            'status' => $request->input('status'),
            'property_type' => $request->input('property_type'),
            'property_type_id' => $request->input('property_type_id'),
            'keywords' => $request->input('keywords')
        ];

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['property_type'])) {
            $query->where('property_type', $filters['property_type']);
        }

        if (!empty($filters['property_type_id'])) {
            $query->whereHas('propertySubType', function ($q) use ($filters) {
                $q->where('property_type_id', $filters['property_type_id']);
            });
        }

        if (!empty($filters['keywords'])) {
            $keywords = $filters['keywords'];
            $query->where(function ($q) use ($keywords) {
                $q->where('title', 'like', '%' . $keywords . '%')
                  ->orWhere('description', 'like', '%' . $keywords . '%')
                  ->orWhere('city', 'like', '%' . $keywords . '%')
                  ->orWhere('state_region', 'like', '%' . $keywords . '%');
            });
        }

        $properties = $query->with('user')->paginate(10)->appends($filters);
        $propertyTypes = PropertyType::all();
        return view('admin.properties.index', compact('properties', 'filters', 'propertyTypes'));
    }

    /**
     * Update the status of a property (approve/reject/sold/rented).
     */
    public function updatePropertyStatus(Request $request, Property $property)
    {
        $request->validate([
            'status' => 'required|in:draft,published,sold,rented,under_offer',
        ]);

        $property->status = $request->input('status');
        $property->save();

        return back()->with('success', 'Property status updated successfully.');
    }
    
    public function toggleFeatured(Property $property)
    {
        $property->update(['is_featured' => !$property->is_featured]);
        
        return back()->with('success', 'Property featured status updated.');
    }

    /**
     * Show the form for editing the specified property (admin).
     */
    public function editProperty(Property $property)
    {
        // Reuse the existing property edit view
        return view('properties.edit', compact('property'));
    }

    /**
     * Update the specified property in storage (admin).
     * This method will be similar to PropertyController@update but without user_id check.
     */
    public function updateProperty(Request $request, Property $property)
    {
        $request->validate([
            'property_type' => 'required|string|max:255',
            'listing_type' => 'required|string|in:For Sale,For Rent',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'nullable|string|max:10',
            'address_line_1' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state_region' => 'nullable|string|max:255',
            'zip_code' => 'nullable|string|max:20',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'features' => 'nullable|string',
            'status' => 'nullable|string|in:draft,published,sold,rented,under_offer',
        ]);

        // Parse features JSON string to array
        $features = json_decode($request->input('features', '{}'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return back()->withErrors(['features' => 'The features field must be a valid JSON string.'])->withInput();
        }

        $property->fill($request->except(['images', 'features']));
        $property->currency = $request->input('currency', 'USD');

        // Handle image uploads (add new, keep existing if not replaced)
        $imagePaths = $property->images ?? [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $filename = uniqid() . '.' . $image->getClientOriginalExtension();
                $path = 'public/properties/' . $filename;

                // Use Intervention Image to resize and save
                $manager = new ImageManager(new Driver());
                $manager->read($image)->resize(800, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                })->save(storage_path('app/' . $path));

                $imagePaths[] = str_replace('public/', '', $path); // Store relative path only
            }
        }
        $property->images = $imagePaths;

        // Assign parsed features
        $property->features = $features;

        $property->save();

        return redirect()->route('admin.properties.index')->with('success', 'Property updated successfully by admin!');
    }

    /**
     * Remove the specified property from storage (admin).
     */
    public function destroyProperty(Property $property)
    {
        // Delete associated images from storage
        if ($property->images) {
            foreach ($property->images as $imagePath) {
                Storage::delete(str_replace('/storage/', 'public/', $imagePath));
            }
        }

        $property->delete();

        return back()->with('success', 'Property deleted successfully by admin!');
    }
}
