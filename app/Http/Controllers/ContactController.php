<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Mail\PropertyInquiry;
use Illuminate\Support\Facades\Mail;
use App\Models\Property; // Import Property model
use App\Models\User; // Import User model

class ContactController extends Controller
{
    /**
     * Display the contact form.
     *
     * @return \Illuminate\View\View
     */
    public function showContactForm()
    {
        return view('contact.form');
    }

    /**
     * Handle the submission of the contact form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitContactForm(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string',
        ]);

        // Here you would typically send an email or save to a database
        // For now, let's just redirect with a success message
        return redirect()->back()->with('success', 'Your message has been sent successfully!');
    }

    /**
     * Send an inquiry about a specific property.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Property  $property
     * @return \Illuminate\Http\RedirectResponse
     */
    public function send(Request $request, Property $property)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string',
        ]);

        $lister = $property->user; // Assuming a property belongs to a user (lister)

        if (!$lister) {
            return back()->with('error', 'Lister not found for this property.');
        }

        Mail::to($lister->email)->send(new PropertyInquiry(
            $property,
            $request->first_name . ' ' . $request->last_name,
            $request->email,
            $request->phone,
            $request->message
        ));

        return back()->with('success', 'Your inquiry has been sent to the lister!');
    }
}
