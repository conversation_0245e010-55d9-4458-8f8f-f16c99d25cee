<?php

namespace App\Http\Controllers;

use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class PropertyController extends Controller
{
    /**
     * Display a listing of the properties.
     */
    public function index()
    {
        $properties = Cache::remember('all_published_properties', 60, function () {
            return Property::where('status', 'published')->get();
        });

        return view('properties.index', compact('properties'));
    }

    /**
     * Display the specified property.
     */
    public function show(Property $property)
    {
        if ($property->status !== 'published' && (!Auth::check() || Auth::id() !== $property->user_id)) {
            abort(404); // Only published properties are publicly viewable, or owner can view drafts
        }
        return view('properties.show', compact('property'));
    }




}
